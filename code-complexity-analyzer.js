/**
 * 代码变更复杂度评估器
 * 纯JavaScript实现，无外部依赖
 * 支持多种编程语言的复杂度评估
 */
class CodeComplexityAnalyzer {
    constructor() {
        this.languageConfigs = this.initLanguageConfigs();
        this.compiledFilePatterns = this.initCompiledFilePatterns();
    }

    /**
     * 初始化语言配置
     */
    initLanguageConfigs() {
        return {
            javascript: {
                extensions: ['.js', '.jsx', '.mjs', '.cjs'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'function', 'class', 'async', 'await'],
                complexOperators: ['&&', '||', '??', '?.', '...'],
                functionPatterns: [
                    /function\s+\w+\s*\(/g,
                    /\w+\s*=\s*function\s*\(/g,
                    /\w+\s*=\s*\([^)]*\)\s*=>/g,
                    /\w+\s*:\s*function\s*\(/g
                ],
                classPatterns: [/class\s+\w+/g],
                complexityWeight: 1.0
            },
            typescript: {
                extensions: ['.ts', '.tsx'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'function', 'class', 'async', 'await', 'interface', 'type', 'enum', 'namespace'],
                complexOperators: ['&&', '||', '??', '?.', '...', 'as', 'is'],
                functionPatterns: [
                    /function\s+\w+\s*\(/g,
                    /\w+\s*=\s*function\s*\(/g,
                    /\w+\s*=\s*\([^)]*\)\s*=>/g,
                    /\w+\s*:\s*function\s*\(/g,
                    /\w+\s*\([^)]*\)\s*:\s*\w+/g
                ],
                classPatterns: [/class\s+\w+/g, /interface\s+\w+/g, /type\s+\w+/g],
                complexityWeight: 1.2
            },
            python: {
                extensions: ['.py', '.pyx', '.pyi'],
                keywords: ['if', 'elif', 'else', 'for', 'while', 'try', 'except', 'finally', 'def', 'class', 'async', 'await', 'with', 'match', 'case'],
                complexOperators: ['and', 'or', 'not', 'in', 'is', 'lambda'],
                functionPatterns: [/def\s+\w+\s*\(/g, /async\s+def\s+\w+\s*\(/g],
                classPatterns: [/class\s+\w+/g],
                complexityWeight: 1.0
            },
            java: {
                extensions: ['.java'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'finally', 'class', 'interface', 'enum', 'synchronized'],
                complexOperators: ['&&', '||', '!', 'instanceof'],
                functionPatterns: [
                    /public\s+\w+\s+\w+\s*\(/g,
                    /private\s+\w+\s+\w+\s*\(/g,
                    /protected\s+\w+\s+\w+\s*\(/g,
                    /\w+\s+\w+\s*\(/g
                ],
                classPatterns: [/class\s+\w+/g, /interface\s+\w+/g, /enum\s+\w+/g],
                complexityWeight: 1.1
            },
            go: {
                extensions: ['.go'],
                keywords: ['if', 'else', 'for', 'switch', 'case', 'select', 'func', 'type', 'struct', 'interface', 'go', 'defer', 'chan'],
                complexOperators: ['&&', '||', '!', '<-', ':='],
                functionPatterns: [/func\s+\w+\s*\(/g, /func\s*\([^)]*\)\s*\w+\s*\(/g],
                classPatterns: [/type\s+\w+\s+struct/g, /type\s+\w+\s+interface/g],
                complexityWeight: 1.0
            },
            dart: {
                extensions: ['.dart'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'finally', 'class', 'abstract', 'async', 'await'],
                complexOperators: ['&&', '||', '!', '??', '?.', '...'],
                functionPatterns: [/\w+\s+\w+\s*\(/g, /\w+\s*\(/g],
                classPatterns: [/class\s+\w+/g, /abstract\s+class\s+\w+/g],
                complexityWeight: 1.0
            },
            cpp: {
                extensions: ['.cpp', '.cc', '.cxx', '.c', '.h', '.hpp', '.hxx'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'class', 'struct', 'template', 'namespace'],
                complexOperators: ['&&', '||', '!', '->', '::', '.*', '->*'],
                functionPatterns: [
                    /\w+\s+\w+\s*\(/g,
                    /\w+::\w+\s*\(/g,
                    /template\s*<[^>]*>\s*\w+\s+\w+\s*\(/g
                ],
                classPatterns: [/class\s+\w+/g, /struct\s+\w+/g, /template\s*<[^>]*>\s*class\s+\w+/g],
                complexityWeight: 1.3
            },
            kotlin: {
                extensions: ['.kt', '.kts'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'when', 'try', 'catch', 'finally', 'fun', 'class', 'object', 'interface'],
                complexOperators: ['&&', '||', '!', '?:', '?.', 'is', 'as'],
                functionPatterns: [/fun\s+\w+\s*\(/g],
                classPatterns: [/class\s+\w+/g, /object\s+\w+/g, /interface\s+\w+/g],
                complexityWeight: 1.0
            },
            swift: {
                extensions: ['.swift'],
                keywords: ['if', 'else', 'for', 'while', 'repeat', 'switch', 'case', 'do', 'try', 'catch', 'func', 'class', 'struct', 'enum', 'protocol'],
                complexOperators: ['&&', '||', '!', '??', '?.', 'is', 'as'],
                functionPatterns: [/func\s+\w+\s*\(/g],
                classPatterns: [/class\s+\w+/g, /struct\s+\w+/g, /enum\s+\w+/g, /protocol\s+\w+/g],
                complexityWeight: 1.0
            },
            html: {
                extensions: ['.html', '.htm'],
                keywords: [],
                complexOperators: [],
                functionPatterns: [],
                classPatterns: [],
                complexityWeight: 0.3
            },
            css: {
                extensions: ['.css', '.less', '.sass', '.scss'],
                keywords: [],
                complexOperators: [],
                functionPatterns: [/@function\s+\w+/g, /@mixin\s+\w+/g],
                classPatterns: [/\.\w+/g, /#\w+/g],
                complexityWeight: 0.4
            },
            xml: {
                extensions: ['.xml', '.wxml', '.xhtml'],
                keywords: [],
                complexOperators: [],
                functionPatterns: [],
                classPatterns: [],
                complexityWeight: 0.3
            },
            vue: {
                extensions: ['.vue'],
                keywords: ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'function', 'class', 'async', 'await'],
                complexOperators: ['&&', '||', '??', '?.', '...', 'v-if', 'v-for', 'v-show'],
                functionPatterns: [
                    /function\s+\w+\s*\(/g,
                    /\w+\s*=\s*function\s*\(/g,
                    /\w+\s*=\s*\([^)]*\)\s*=>/g,
                    /methods:\s*{[^}]*}/g
                ],
                classPatterns: [/class\s+\w+/g, /<template>/g, /<script>/g, /<style>/g],
                complexityWeight: 1.1
            }
        };
    }

    /**
     * 初始化编译产物文件模式
     */
    initCompiledFilePatterns() {
        return [
            /\.min\.(js|css)$/,
            /\.bundle\.(js|css)$/,
            /\.chunk\.\w+\.(js|css)$/,
            /\.(map|d\.ts)$/,
            /node_modules\//,
            /dist\//,
            /build\//,
            /\.git\//,
            /coverage\//,
            /\.nyc_output\//,
            /\.next\//,
            /\.nuxt\//,
            /\.vuepress\//,
            /public\/.*\.(js|css)$/,
            /static\/.*\.(js|css)$/,
            /assets\/.*\.(js|css)$/,
            /vendor\//,
            /lib\/.*\.(js|css)$/,
            /\w{8,}\.(js|css)$/,
            /.*\.generated\./,
            /.*\.compiled\./,
            /.*\.optimized\./
        ];
    }

    /**
     * 检查文件是否为编译产物
     */
    isCompiledFile(filePath) {
        return this.compiledFilePatterns.some(pattern => pattern.test(filePath));
    }

    /**
     * 根据文件扩展名获取语言类型
     */
    getLanguageType(filePath) {
        const ext = filePath.toLowerCase().match(/\.[^.]+$/)?.[0] || '';
        
        for (const [langType, config] of Object.entries(this.languageConfigs)) {
            if (config.extensions.includes(ext)) {
                return langType;
            }
        }
        
        return 'unknown';
    }

    /**
     * 解析git diff
     */
    parseDiff(diffText) {
        const files = [];
        const diffLines = diffText.split('\n');
        let currentFile = null;
        let currentHunk = null;

        for (let i = 0; i < diffLines.length; i++) {
            const line = diffLines[i];

            if (line.startsWith('diff --git')) {
                if (currentFile) {
                    files.push(currentFile);
                }
                
                const match = line.match(/diff --git a\/(.+?) b\/(.+)/);
                if (match) {
                    currentFile = {
                        oldPath: match[1],
                        newPath: match[2],
                        hunks: [],
                        isDeleted: false,
                        isNew: false
                    };
                }
            } else if (line.startsWith('deleted file mode')) {
                if (currentFile) currentFile.isDeleted = true;
            } else if (line.startsWith('new file mode')) {
                if (currentFile) currentFile.isNew = true;
            } else if (line.startsWith('@@')) {
                const match = line.match(/@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@/);
                if (match && currentFile) {
                    currentHunk = {
                        oldStart: parseInt(match[1]),
                        oldLines: parseInt(match[2]) || 1,
                        newStart: parseInt(match[3]),
                        newLines: parseInt(match[4]) || 1,
                        lines: []
                    };
                    currentFile.hunks.push(currentHunk);
                }
            } else if (currentHunk && (line.startsWith('+') || line.startsWith('-') || line.startsWith(' '))) {
                currentHunk.lines.push({
                    type: line[0] === '+' ? 'added' : line[0] === '-' ? 'removed' : 'context',
                    content: line.substring(1)
                });
            }
        }

        if (currentFile) {
            files.push(currentFile);
        }

        return files;
    }

    /**
     * 计算单行代码复杂度
     */
    calculateLineComplexity(line, languageConfig) {
        let complexity = 0;
        const content = line.trim();
        
        if (!content || content.startsWith('//') || content.startsWith('/*') || 
            content.startsWith('#') || content.startsWith('*')) {
            return 0;
        }

        complexity += content.length * 0.01;

        languageConfig.keywords.forEach(keyword => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            const matches = content.match(regex);
            if (matches) {
                complexity += matches.length * 2;
            }
        });

        languageConfig.complexOperators.forEach(operator => {
            const escapedOp = operator.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(escapedOp, 'g');
            const matches = content.match(regex);
            if (matches) {
                complexity += matches.length * 1.5;
            }
        });

        languageConfig.functionPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                complexity += matches.length * 5;
            }
        });

        languageConfig.classPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                complexity += matches.length * 8;
            }
        });

        const nestedBraces = (content.match(/[{[(]/g) || []).length;
        complexity += nestedBraces * 1.5;

        const conditionalComplexity = (content.match(/[?:]/g) || []).length;
        complexity += conditionalComplexity * 2;

        const stringComplexity = (content.match(/["'`]/g) || []).length / 2;
        complexity += stringComplexity * 0.5;

        return complexity;
    }

    /**
     * 计算文件复杂度
     */
    calculateFileComplexity(file) {
        if (this.isCompiledFile(file.newPath) || this.isCompiledFile(file.oldPath)) {
            return {
                fileName: file.newPath || file.oldPath,
                complexity: 0,
                reason: 'Compiled file filtered out',
                languageType: 'compiled',
                linesAdded: 0,
                linesRemoved: 0,
                linesModified: 0
            };
        }

        const languageType = this.getLanguageType(file.newPath || file.oldPath);
        const languageConfig = this.languageConfigs[languageType];

        if (!languageConfig) {
            return {
                fileName: file.newPath || file.oldPath,
                complexity: 0,
                reason: 'Unsupported language',
                languageType: 'unknown',
                linesAdded: 0,
                linesRemoved: 0,
                linesModified: 0
            };
        }

        let totalComplexity = 0;
        let linesAdded = 0;
        let linesRemoved = 0;
        let linesModified = 0;

        if (file.isNew) {
            totalComplexity += 10;
        }
        
        if (file.isDeleted) {
            totalComplexity += 5;
        }

        file.hunks.forEach(hunk => {
            hunk.lines.forEach(line => {
                const lineComplexity = this.calculateLineComplexity(line.content, languageConfig);
                
                if (line.type === 'added') {
                    totalComplexity += lineComplexity * 1.2;
                    linesAdded++;
                } else if (line.type === 'removed') {
                    totalComplexity += lineComplexity * 0.8;
                    linesRemoved++;
                } else if (line.type === 'context') {
                    totalComplexity += lineComplexity * 0.1;
                }
            });
        });

        linesModified = Math.min(linesAdded, linesRemoved);
        const modificationBonus = linesModified * 0.5;
        totalComplexity += modificationBonus;

        totalComplexity *= languageConfig.complexityWeight;

        return {
            fileName: file.newPath || file.oldPath,
            complexity: Math.round(totalComplexity * 100) / 100,
            languageType: languageType,
            linesAdded: linesAdded,
            linesRemoved: linesRemoved,
            linesModified: linesModified,
            isNew: file.isNew,
            isDeleted: file.isDeleted
        };
    }

    /**
     * 解析GitLab API格式的数据
     */
    parseGitLabData(gitlabData) {
        if (Array.isArray(gitlabData)) {
            return gitlabData.map(item => this.convertGitLabItemToFile(item));
        } else if (gitlabData && typeof gitlabData === 'object') {
            return [this.convertGitLabItemToFile(gitlabData)];
        }
        return [];
    }

    /**
     * 将GitLab格式的数据转换为内部文件格式
     */
    convertGitLabItemToFile(item) {
        const file = {
            oldPath: item.old_path || item.oldPath,
            newPath: item.new_path || item.newPath,
            hunks: [],
            isDeleted: item.deleted_file || item.deletedFile || false,
            isNew: item.new_file || item.newFile || false
        };

        if (item.diff) {
            file.hunks = this.parseGitLabDiff(item.diff);
        }

        return file;
    }

    /**
     * 解析GitLab diff内容
     */
    parseGitLabDiff(diffText) {
        const hunks = [];
        const lines = diffText.split('\n');
        let currentHunk = null;

        for (const line of lines) {
            if (line.startsWith('@@')) {
                const match = line.match(/@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@/);
                if (match) {
                    if (currentHunk) {
                        hunks.push(currentHunk);
                    }
                    currentHunk = {
                        oldStart: parseInt(match[1]),
                        oldLines: parseInt(match[2]) || 1,
                        newStart: parseInt(match[3]),
                        newLines: parseInt(match[4]) || 1,
                        lines: []
                    };
                }
            } else if (currentHunk && (line.startsWith('+') || line.startsWith('-') || line.startsWith(' '))) {
                currentHunk.lines.push({
                    type: line[0] === '+' ? 'added' : line[0] === '-' ? 'removed' : 'context',
                    content: line.substring(1)
                });
            }
        }

        if (currentHunk) {
            hunks.push(currentHunk);
        }

        return hunks;
    }

    /**
     * 分析代码变更复杂度
     */
    analyze(input) {
        if (!input) {
            return {
                totalComplexity: 0,
                files: [],
                summary: {
                    totalFiles: 0,
                    newFiles: 0,
                    deletedFiles: 0,
                    modifiedFiles: 0,
                    filteredFiles: 0,
                    totalLinesAdded: 0,
                    totalLinesRemoved: 0,
                    languageDistribution: {}
                }
            };
        }

        let parsedFiles;

        if (typeof input === 'string') {
            parsedFiles = this.parseDiff(input);
        } else if (typeof input === 'object') {
            parsedFiles = this.parseGitLabData(input);
        } else {
            throw new Error('Unsupported input format. Expected string (git diff) or object/array (GitLab API format)');
        }
        
        const fileResults = parsedFiles.map(file => this.calculateFileComplexity(file));
        
        const totalComplexity = fileResults.reduce((sum, file) => sum + file.complexity, 0);
        
        const summary = this.generateSummary(fileResults);

        return {
            totalComplexity: Math.round(totalComplexity * 100) / 100,
            files: fileResults.sort((a, b) => b.complexity - a.complexity),
            summary: summary
        };
    }

    /**
     * 生成分析摘要
     */
    generateSummary(fileResults) {
        const summary = {
            totalFiles: fileResults.length,
            newFiles: 0,
            deletedFiles: 0,
            modifiedFiles: 0,
            filteredFiles: 0,
            totalLinesAdded: 0,
            totalLinesRemoved: 0,
            languageDistribution: {}
        };

        fileResults.forEach(file => {
            if (file.isNew) summary.newFiles++;
            if (file.isDeleted) summary.deletedFiles++;
            if (file.linesAdded > 0 || file.linesRemoved > 0) summary.modifiedFiles++;
            if (file.complexity === 0 && file.reason) summary.filteredFiles++;
            
            summary.totalLinesAdded += file.linesAdded;
            summary.totalLinesRemoved += file.linesRemoved;
            
            if (!summary.languageDistribution[file.languageType]) {
                summary.languageDistribution[file.languageType] = {
                    files: 0,
                    complexity: 0
                };
            }
            summary.languageDistribution[file.languageType].files++;
            summary.languageDistribution[file.languageType].complexity += file.complexity;
        });

        return summary;
    }
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = CodeComplexityAnalyzer;
}

if (typeof window !== 'undefined') {
    window.CodeComplexityAnalyzer = CodeComplexityAnalyzer;
}