
<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>大模型成本计算器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        h1 {
            margin: -20px 0 0;
            font-size: 25px;
            line-height: 80px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }

        button {
            padding: 10px 20px;
            background-color: #007BFF;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            line-height: 30px;
            width: 100%;
        }

        button:hover {
            background-color: #0056b3;
        }

        .tag {
            display: inline-block;
            border-radius: 5px;
            border: 1px solid #cc9d40;
            background: #fff6e4;
            color: #916500;
            font-size: 13px;
            font-weight: bolder;
            line-height: 25px;
            padding: 0 10px;
            margin-right: 5px;
        }

        .price {
            margin-bottom: 15px;
            line-height: 30px;
            font-size: 13px;
        }

        .result {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #f8f9fa;
            border: 1px solid #ccc;
            border-radius: 5px;
            color: #666;
        }
    </style>
</head>
<body>
<div class='container'>
    <h1>大模型成本计算器</h1>
    <div class='input-group'>
        <label for='model'>选择模型</label>
        <select id='model'></select>
    </div>
    <div class='input-group'>
        <label for='inputTokens'>输入 Tokens 数量</label>
        <input type='number' id='inputTokens' placeholder='例如：50000'>
    </div>
    <div class='input-group'>
        <label for='outputTokens'>输出 Tokens 数量</label>
        <input type='number' id='outputTokens' placeholder='例如：20000'>
    </div>
    <div class='price' id='price'></div>
    <button onclick='calculateCost()'>开始计算</button>
    <div class='result' id='result'>请填写输入和输出Tokens数量...</div>
</div>

<script>
    class Storage {
        constructor(storageType) {
            this.storage = storageType === 'session' ? sessionStorage : localStorage;
        }

        set(key, value) {
            this.storage.setItem(key, JSON.stringify(value));
        }

        get(key) {
            const item = this.storage.getItem(key);
            return item ? JSON.parse(item) : null;
        }

        remove(key) {
            this.storage.removeItem(key);
        }

        clear() {
            this.storage.clear();
        }
    }

    const storage = new Storage('session');
    const pricing = {
        // Anthropic Claude models (USD per token)
        'claude-3-5-sonnet-20240620': { input: 3 / 1000000, output: 15 / 1000000, currency: 'USD' },
        'claude-3-opus-20240229': { input: 15 / 1000000, output: 75 / 1000000, currency: 'USD' },
        'claude-3-sonnet-20240229': { input: 3 / 1000000, output: 15 / 1000000, currency: 'USD' },
        'claude-3-haiku-20240307': { input: .25 / 1000000, output: 1.25 / 1000000, currency: 'USD' },
        'claude-4-sonnet': { input: 3 / 1000000, output: 15 / 1000000, currency: 'USD' },
        'claude-4-opus': { input: 15 / 1000000, output: 75 / 1000000, currency: 'USD' },

        // OpenAI models (USD per token)
        'gpt-4o': { input: 5 / 1000000, output: 15 / 1000000, currency: 'USD' },
        'gpt-4o-mini': { input: .15 / 1000000, output: .6 / 1000000, currency: 'USD' },
        'gpt-4-turbo': { input: 10 / 1000000, output: 30 / 1000000, currency: 'USD' },
        'gpt-4': { input: 30 / 1000000, output: 60 / 1000000, currency: 'USD' },
        'gpt-3.5-turbo': { input: 3 / 1000000, output: 6 / 1000000, currency: 'USD' },

        // Alibaba Qwen models (CNY per token)
        'qwen-turbo': { input: 0.002 / 1000, output: 0.006 / 1000, currency: 'CNY' },
        'qwen-plus': { input: 0.004 / 1000, output: 0.012 / 1000, currency: 'CNY' },
        'qwen-max': { input: 0.04 / 1000, output: 0.12 / 1000, currency: 'CNY' },
        'qwen-long': { input: 0.0005 / 1000, output: 0.002 / 1000, currency: 'CNY' },
        'qwen-vl-plus': { input: 0.008 / 1000, output: 0.008 / 1000, currency: 'CNY' },
        'qwen-vl-max': { input: 0.02 / 1000, output: 0.02 / 1000, currency: 'CNY' },
    };

    const units = {
        USD: '$',
        CNY: '¥',
    };

    const getElementById = (id) => document.getElementById(id);
    const render = (target, content) => {
        if (typeof target === 'string') {
            target = getElementById(target);
        }
        target.innerHTML = content;
    };

    render('model', Object.keys(pricing).map((model) => `<option value='${model}'>${model}</option>`).join(''));

    getElementById('model').addEventListener('change', (e) => {
        const { value: model } = e.target;
        const { input, output, currency } = pricing[model];
        const unit = units[currency];
        render('price', `<span class='tag'>${model}</span> 模型价格：${unit}${(input * 1000000).toFixed(2)}/1M input_tokens，${unit}${(output * 1000000).toFixed(2)}/1M output_tokens`);
    });

    getElementById('model').dispatchEvent(new Event('change'));

    async function fetchExchangeRate(base, target) {
        const apiKey = '269278edf710006669f0615a';
        const url = `https://v6.exchangerate-api.com/v6/${apiKey}/latest/${base}`;
        const key = `EXCHANGE_RATE_${base}_${target}`;
        const rates = storage.get(key);
        if (rates) {
            return rates[target];
        }
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            const data = await response.json();
            const rates = data.conversion_rates;
            storage.set(key, rates);
            return rates[target];
        } catch (error) {
            console.error('Error fetching exchange rate:', error);
            return null;
        }
    }

    async function calculateCost() {
        const model = getElementById('model').value;
        const inputTokens = parseFloat(getElementById('inputTokens').value);
        const outputTokens = parseFloat(getElementById('outputTokens').value);

        if (isNaN(inputTokens) || isNaN(outputTokens)) {
            alert('请确保所有输入都是数字。');
            return;
        }

        const modelPricing = pricing[model];
        let totalInputCost = inputTokens * modelPricing.input;
        let totalOutputCost = outputTokens * modelPricing.output;
        let totalCost = totalInputCost + totalOutputCost;

        if (modelPricing.currency !== 'CNY') {
            render('result', '正在获取最新汇率...');
            const exchangeRate = await fetchExchangeRate(modelPricing.currency, 'CNY');
            if (exchangeRate) {
                totalInputCost *= exchangeRate;
                totalOutputCost *= exchangeRate;
                totalCost *= exchangeRate;
            } else {
                alert('无法获取汇率数据，请稍后再试。');
                return;
            }
        }
        render('result', `总成本：${totalCost.toFixed(2)} 元（输入成本：${totalInputCost.toFixed(2)} 元，输出成本：${totalOutputCost.toFixed(2)} 元）`);
    }
</script>
</body>
</html>