function calcWorkdays(start, end, workWindows = [['9:00', '12:00'], ['13:00', '18:00']]) {
    const s = new Date(start);
    const e = new Date(end);
    if ((isNaN(s) || isNaN(e)) || e <= s) return 0;

    const WORK_WINDOWS = workWindows.map(([startTime, endTime]) => {
        const [startHour, startMinute] = startTime.split(':').map(Number);
        const [endHour, endMinute] = endTime.split(':').map(Number);
        return {sh: startHour, sm: startMinute || 0, eh: endHour, em: endMinute || 0};
    });

    const minutesInDay = 8 * 60; // 8 小时 = 1 人日
    let totalMinutes = 0;

    let cursor = new Date(s.getFullYear(), s.getMonth(), s.getDate());
    const endDay = new Date(e.getFullYear(), e.getMonth(), e.getDate());

    while (cursor <= endDay) {
        for (const w of WORK_WINDOWS) {
            const windowStart = new Date(
                cursor.getFullYear(),
                cursor.getMonth(),
                cursor.getDate(),
                w.sh, w.sm, 0, 0
            );
            const windowEnd = new Date(
                cursor.getFullYear(),
                cursor.getMonth(),
                cursor.getDate(),
                w.eh, w.em, 0, 0
            );
            const segStart = new Date(Math.max(windowStart.getTime(), s.getTime()));
            const segEnd = new Date(Math.min(windowEnd.getTime(), e.getTime()));

            if (segEnd > segStart) {
                totalMinutes += (segEnd - segStart) / 60000;
            }
        }
        cursor.setDate(cursor.getDate() + 1);
        cursor.setHours(0, 0, 0, 0);
    }
    const days = totalMinutes / minutesInDay;
    const roundedToHalf = Math.round(days * 2) / 2;
    return Number(roundedToHalf.toFixed(1));
}

function getTime(value) {
    return value ? new Date(value).getTime() : null
}

function getMergeRequest(value) {
    return value ? ({text: value.replace(/(.*\/)(\d+)$/, "MR!$2"), link: value}) : null
}

function getJiraLink(key, text) {
    return {text: text || key, link: `https://jira.wosai-inc.com/browse/${key}`}
}

function getProject(text, mrLink) {
    return (text && mrLink) ? ({text, link: mrLink.replace(/(.*)\/-\/merge_requests\/(\d+)$/, "$1")}) : null
}

function handler(input) {
    return input.map(({id, key, fields}) => {
        return {
            Jira: getJiraLink(key, fields.summary),
            ID: id,
            问题类型: {"Sub-task": "子任务"}[fields.issuetype?.name] || fields.issuetype?.name,
            父问题: getJiraLink(fields.parent.key),
            父问题类型: fields.parent.fields.issuetype.name,
            优先级: fields.priority.name,
            概要: fields.summary,
            描述: fields.description,
            经办人邮箱: fields.assignee.emailAddress || fields.reporter.emailAddress,
            计划开始时间: getTime(fields.customfield_12501),
            计划完成时间: getTime(fields.customfield_13900 || fields.customfield_14900),
            任务状态: fields.status.name,
            最近更新时间: getTime(fields.updated),
            MR: getMergeRequest(fields.customfield_11100),
            任务类型: fields.customfield_14202?.value,
            工程项目: getProject(fields.customfield_10900, fields.customfield_11100),
            预估人日: calcWorkdays(fields.customfield_12501, fields.customfield_13900 || fields.customfield_14900),
            任务编号: getJiraLink(key)
        }
    })
}