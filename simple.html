<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版地址聚类工具</title>
    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.2.47/vue.global.min.js"></script>
    <!-- 引入高德地图API -->
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: '76317e580e8ba2fd846d381584bb2f71',
        }
    </script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=76317e580e8ba2fd846d381584bb2f71"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
        }
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        .sidebar {
            width: 300px;
            background-color: #fff;
            padding: 1rem;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }
        .map-container {
            flex: 1;
            position: relative;
        }
        #map {
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .panel {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: #f9f9f9;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin-top: 10px;
        }
        .debug {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            max-height: 200px;
            overflow: auto;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div id="app">
        <header>
            <h1>简化版地址聚类工具</h1>
        </header>
        
        <div class="container">
            <div class="sidebar">
                <div class="panel">
                    <h2>地图控制</h2>
                    <button @click="initMap">初始化地图</button>
                    <button @click="addMarker">添加标记</button>
                    <p v-if="initError" style="color: red;">{{ initError }}</p>
                </div>
            </div>
            
            <div class="map-container">
                <div id="map"></div>
            </div>
        </div>
    </div>

    <div class="debug">
        <h3>调试信息</h3>
        <div id="debug-content"></div>
    </div>

    <script>
        // 添加调试函数
        function debug(message) {
            console.log(message);
            const debugContent = document.getElementById('debug-content');
            if (debugContent) {
                const p = document.createElement('p');
                p.textContent = message;
                debugContent.appendChild(p);
            }
        }

        debug('页面加载完成');

        // 创建Vue应用
        const app = Vue.createApp({
            data() {
                return {
                    map: null,
                    initError: null
                };
            },
            methods: {
                initMap() {
                    debug('开始初始化地图');
                    try {
                        if (typeof AMap === 'undefined') {
                            throw new Error('高德地图API未加载');
                        }
                        
                        this.map = new AMap.Map('map', {
                            zoom: 10,
                            center: [116.397428, 39.90923],
                            viewMode: '2D'
                        });
                        
                        debug('地图初始化成功');
                        this.initError = null;
                    } catch (error) {
                        debug('地图初始化失败: ' + error.message);
                        this.initError = '地图初始化失败: ' + error.message;
                        console.error('地图初始化失败:', error);
                    }
                },
                addMarker() {
                    debug('开始添加标记');
                    try {
                        if (!this.map) {
                            throw new Error('地图未初始化');
                        }
                        
                        const marker = new AMap.Marker({
                            position: [116.397428, 39.90923],
                            title: '测试标记'
                        });
                        
                        this.map.add(marker);
                        debug('标记添加成功');
                    } catch (error) {
                        debug('添加标记失败: ' + error.message);
                        console.error('添加标记失败:', error);
                    }
                }
            },
            mounted() {
                debug('Vue应用已挂载');
            }
        });

        // 挂载Vue应用
        try {
            debug('准备挂载Vue应用');
            app.mount('#app');
            debug('Vue应用挂载成功');
        } catch (error) {
            debug('Vue应用挂载失败: ' + error.message);
            console.error('Vue应用挂载失败:', error);
        }
    </script>
</body>
</html>
