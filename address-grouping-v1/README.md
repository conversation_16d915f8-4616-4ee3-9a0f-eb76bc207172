# 地址坐标分组工具

这是一个基于Web的工具，用于将地址坐标按照就近原则分组。它使用k-means算法根据坐标将地址分成多个组，并在高德地图上可视化显示。

## 功能特点

- 上传Excel文件导入地址数据
- 基于坐标的k-means聚类分组
- 高德地图可视化展示
- 使用矩形框标示分组
- 导出带分组信息的Excel文件
- 实时显示计算进度
- 支持加载示例数据进行测试

## 使用方法

### 安装依赖

本工具是纯前端应用，不需要安装任何依赖。但为了解决浏览器安全限制，需要通过HTTP服务器访问。

### 启动服务器

1. 确保已安装Node.js
2. 在项目目录下运行以下命令启动内置的HTTP服务器：

```bash
node server.js
```

3. 在浏览器中访问 http://localhost:3000

### 使用流程

1. 点击"加载示例数据"按钮加载示例数据，或者上传自己的Excel文件
   - Excel文件格式要求：包含"门店号"、"门店名称"、"省份"、"城市"、"区"、"详细地址"、"坐标"列
   - 坐标格式：经度,纬度，例如："121.548613,31.289272"

2. 设置分组数量（默认为10）

3. 点击"开始分组"按钮，系统将使用k-means算法进行分组计算
   - 计算过程中会显示进度条
   - 可以点击"取消"按钮中断计算

4. 计算完成后，地图上会显示分组结果
   - 每个分组用不同颜色的矩形框标示
   - 右侧面板显示各分组的统计信息

5. 点击"导出结果"按钮，将分组结果导出为Excel文件
   - 导出的文件在原有数据基础上增加"分组序号"列

## 技术实现

- 前端框架：Vue 3
- UI组件：Element Plus
- 地图服务：高德地图 JavaScript API
- Excel处理：SheetJS/xlsx
- 聚类算法：k-means（Web Worker实现）

## 注意事项

- 由于浏览器安全限制，必须通过HTTP服务器访问应用
- 大量数据点（>1000）可能会影响地图渲染性能
- 坐标格式必须正确，否则可能导致分组结果不准确
