// Web Worker for address grouping

// 状态变量
let running = false;
let points = [];
let k = 10;
let currentIteration = 0;

// 监听消息
self.onmessage = function(e) {
    const { type, payload } = e.data;
    
    if (type === 'process') {
        // 开始处理
        points = payload.points;
        k = payload.k || 10;
        startGrouping();
    } else if (type === 'cancel') {
        // 取消处理
        running = false;
    }
};

// 开始分组
function startGrouping() {
    if (points.length === 0) {
        self.postMessage({
            type: 'result',
            payload: {
                clusters: []
            }
        });
        return;
    }
    
    running = true;
    currentIteration = 0;
    
    // 初始化进度
    updateProgress(0);
    
    // 执行分组算法
    groupByProvince(points, k);
}

// 按省份分组，然后在每个省份内部进行扩散分组
async function groupByProvince(data, k) {
    // 按省份分组
    const provinceGroups = {};
    
    data.forEach(point => {
        const province = point.province || '未知省份';
        if (!provinceGroups[province]) {
            provinceGroups[province] = [];
        }
        provinceGroups[province].push(point);
    });
    
    // 计算每个省份应该分配的组数（按比例分配）
    const provinceGroupCounts = {};
    const totalPoints = data.length;
    let remainingGroups = k;
    let remainingPoints = totalPoints;
    
    // 第一轮：按比例分配组数，确保每个省份至少有一个点的地方分配一个组
    Object.keys(provinceGroups).forEach(province => {
        const provincePoints = provinceGroups[province].length;
        if (provincePoints > 0) {
            provinceGroupCounts[province] = 1;
            remainingGroups--;
            remainingPoints -= provincePoints;
        }
    });
    
    // 第二轮：按比例分配剩余的组
    if (remainingGroups > 0 && remainingPoints > 0) {
        Object.keys(provinceGroups).forEach(province => {
            const provincePoints = provinceGroups[province].length;
            const proportion = provincePoints / totalPoints;
            const additionalGroups = Math.round(proportion * k);
            
            // 确保不会分配超过剩余的组数
            const actualAdditional = Math.min(additionalGroups, remainingGroups);
            provinceGroupCounts[province] += actualAdditional;
            remainingGroups -= actualAdditional;
        });
    }
    
    // 如果还有剩余的组，分配给点数最多的省份
    if (remainingGroups > 0) {
        const provincesWithPoints = Object.keys(provinceGroups)
            .filter(province => provinceGroups[province].length > 0)
            .sort((a, b) => provinceGroups[b].length - provinceGroups[a].length);
        
        for (let i = 0; i < remainingGroups && i < provincesWithPoints.length; i++) {
            provinceGroupCounts[provincesWithPoints[i]]++;
        }
    }
    
    // 对每个省份内的点进行分组
    const allResults = [];
    let processedProvinces = 0;
    const totalProvinces = Object.keys(provinceGroups).length;
    
    for (const province in provinceGroups) {
        if (provinceGroups.hasOwnProperty(province)) {
            const provincePoints = provinceGroups[province];
            const provinceGroupCount = provinceGroupCounts[province] || 1;
            
            // 对省份内的点进行扩散分组
            const provinceResults = await spreadGrouping(provincePoints, provinceGroupCount);
            allResults.push(...provinceResults);
            
            // 更新进度
            processedProvinces++;
            const progress = Math.min(Math.round((processedProvinces / totalProvinces) * 100), 99);
            updateProgress(progress);
            
            // 让出CPU时间片
            await new Promise(resolve => setTimeout(resolve, 0));
        }
    }
    
    // 发送结果
    self.postMessage({
        type: 'result',
        payload: {
            clusters: allResults,
            iterations: 1,
            converged: true
        }
    });
    
    running = false;
}

// 扩散分组算法
async function spreadGrouping(provincePoints, groupCount) {
    // 提取坐标
    const coordinates = provincePoints.map(p => [p.lng, p.lat]);
    
    // 找到中心点
    const center = findCenterPoint(coordinates);
    
    // 计算每个点到中心点的距离
    const pointsWithDistance = provincePoints.map((point, index) => ({
        id: point.id,
        distance: calculateDistance([point.lng, point.lat], center)
    }));
    
    // 按距离排序
    pointsWithDistance.sort((a, b) => a.distance - b.distance);
    
    // 计算每组的理想大小
    const idealSize = Math.floor(provincePoints.length / groupCount);
    const remainder = provincePoints.length % groupCount;
    
    // 分配点到组
    const results = [];
    let currentGroup = 0;
    let currentGroupSize = 0;
    
    for (let i = 0; i < pointsWithDistance.length; i++) {
        const point = pointsWithDistance[i];
        
        // 计算当前组的目标大小
        const targetSize = idealSize + (currentGroup < remainder ? 1 : 0);
        
        // 将点分配到当前组
        results.push({
            id: point.id,
            cluster: currentGroup
        });
        
        // 更新当前组大小
        currentGroupSize++;
        
        // 如果当前组已满，开始下一个组
        if (currentGroupSize >= targetSize && currentGroup < groupCount - 1) {
            currentGroup++;
            currentGroupSize = 0;
        }
    }
    
    return results;
}

// 找到一组坐标的中心点
function findCenterPoint(coordinates) {
    if (coordinates.length === 0) return [0, 0];
    
    let sumLng = 0;
    let sumLat = 0;
    
    coordinates.forEach(coord => {
        sumLng += coord[0];
        sumLat += coord[1];
    });
    
    return [sumLng / coordinates.length, sumLat / coordinates.length];
}

// 计算哈弗辛距离（地理距离）
function haversineDistance(point1, point2) {
    // 将经纬度转换为弧度
    const lat1 = point2[1] * Math.PI / 180;
    const lat2 = point1[1] * Math.PI / 180;
    const lon1 = point2[0] * Math.PI / 180;
    const lon2 = point1[0] * Math.PI / 180;
    
    // 半正矢公式
    const dlon = lon2 - lon1;
    const dlat = lat2 - lat1;
    const a = Math.pow(Math.sin(dlat / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(dlon / 2), 2);
    const c = 2 * Math.asin(Math.sqrt(a));
    
    // 地球半径（千米）
    const r = 6371;
    
    // 返回距离（千米）
    return c * r;
}

// 使用哈弗辛距离计算两点之间的距离
function calculateDistance(point1, point2) {
    // 使用哈弗辛公式计算实际地理距离
    return haversineDistance(point1, point2);
}

// 更新进度
function updateProgress(percentage) {
    self.postMessage({
        type: 'progress',
        payload: {
            percentage,
            iteration: currentIteration
        }
    });
}
