<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门店就近分组工具 (高德地图版)</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 高德地图 -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=ed8ae3c141ca2df607dd0fe368e4a189"></script>
    <script type="text/javascript" src="https://webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>
    <!-- XLSX库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        html, body {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        #mapContainer {
            height: 100%;
            width: 100%;
        }
        #controlsPanel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 320px;
            max-height: calc(100vh - 20px);
            background-color: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
            z-index: 1000;
            overflow-y: auto;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        /* 高德地图LabelMarker自定义文本样式 */
        .amap-labelmarker .amap-labelmarker-label {
            /* 可通过JS设置，这里只是示例 */
            /* color: #333; */
            /* background-color: transparent; */
            /* border: none; */
            /* box-shadow: none; */
        }
    </style>
</head>
<body>
<div id="mapContainer"></div>

<div id="controlsPanel">
    <div class="card mb-3">
        <div class="card-header">门店就近分组工具 🗺️</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="kValue" class="form-label">分组数量 (K):</label>
                <input type="number" class="form-control" id="kValue" value="5" min="1" max="100">
            </div>
            
            <div class="mb-3">
                <label for="excelFile" class="form-label">上传Excel文件:</label>
                <input type="file" class="form-control" id="excelFile" accept=".xlsx, .xls">
                <div class="form-text">表头：门店号、门店名称、省份、城市、区、详细地址、坐标</div>
            </div>
            
            <div class="d-grid gap-2">
                <button class="btn btn-primary" onclick="processAndCluster()">🚀 开始分组</button>
                <button class="btn btn-success" id="exportBtn" onclick="exportData()" disabled>📊 导出分组结果</button>
            </div>
        </div>
    </div>
    
    <!-- 不再需要分组结果面板 -->
</div>

<script>
    let map;
    let allStoreData = [];
    let storeLabelMarkers = []; // 存储AMap.LabelMarker对象
    let labelsLayer; // 存储标签图层
    let currentClusterAssignments = []; // 存储当前聚类结果
    let normalMarker; // 用于显示信息窗口的标记
    const defaultMarkerColor = '#007bff'; // 默认标记颜色
    const highlightColor = '#ff0000'; // 高亮颜色（红色）
    // 为不同组别预设一些颜色
    const groupColors = [
        '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
        '#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5',
        '#c49c94', '#f7b6d2', '#c7c7c7', '#dbdb8d', '#9edae5'
    ];
    
    // 全局配置
    const CONFIG = {
        useGeoDistance: true,  // 使用地理距离计算（Haversine公式）
        maxIterations: 300,    // 最大迭代次数
    };
    
    // 原始Excel数据
    let originalExcelData = null;
    let originalHeaders = null;

    function getGroupColor(groupIndex) {
        return groupColors[groupIndex % groupColors.length];
    }

    document.addEventListener('DOMContentLoaded', function() {
        // 设置全局距离计算方法为默认配置
        useGeoDistanceGlobal = CONFIG.useGeoDistance;
        
        map = new AMap.Map('mapContainer', {
            zoom: 5, // 初始中国大陆视野
            center: [104.195397, 35.86166], // 中国大概的中心点
            resizeEnable: true,
            viewMode: '3D',
            mapStyle: 'amap://styles/whitesmoke',
            showLabel: false,
            showIndoorMap: false
        });

        // 创建 AMap.LabelsLayer 图层
        labelsLayer = new AMap.LabelsLayer({
            zooms: [3, 20],
            zIndex: 1000,
            collision: false
        });
        
        // 将图层添加到地图
        map.add(labelsLayer);

        // 添加地图控件
        AMap.plugin(['AMap.ToolBar', 'AMap.Scale', 'AMap.HawkEye'], function(){
            map.addControl(new AMap.ToolBar());
            map.addControl(new AMap.Scale());
            map.addControl(new AMap.HawkEye({isOpen:false})); //鹰眼默认关闭
        });
        
        // 创建普通Marker用于显示信息窗口
        normalMarker = new AMap.Marker({
            anchor: 'bottom-center',
            offset: [0, -15],
        });
    });

    async function processAndCluster() {
        // 获取用户设置
        const kValueInput = document.getElementById('kValue');
        const k = parseInt(kValueInput.value);
        if (isNaN(k) || k <= 0) {
            alert("请输入有效的分组数量K！");
            return;
        }

        const fileInput = document.getElementById('excelFile');
        const file = fileInput.files[0];
        if (!file) {
            alert("请先上传Excel文件！");
            return;
        }

        // 清理旧数据和标记
        labelsLayer.clear(); // 清除图层上的所有标记
        storeLabelMarkers = [];
        allStoreData = [];
        currentClusterAssignments = [];


        const reader = new FileReader();
        reader.onload = async function(e) {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, {type: 'array'});
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, {header:1});

            if (jsonData.length < 2) {
                alert("Excel文件内容为空或表头不正确！"); return;
            }

            const headers = jsonData[0].map(header => String(header).trim());
            const coordIndex = headers.indexOf("坐标");
            const storeIdIndex = headers.indexOf("门店号");
            const storeNameIndex = headers.indexOf("门店名称");
            const addressIndex = headers.indexOf("详细地址");

            if (coordIndex === -1) {
                alert("Excel文件中未找到“坐标”列！"); return;
            }

            const coordinates = [];
            for (let i = 1; i < jsonData.length; i++) {
                const row = jsonData[i];
                if (row && row[coordIndex]) {
                    const coordString = String(row[coordIndex]);
                    const parts = coordString.split(/,|，/);
                    if (parts.length === 2) {
                        const lng = parseFloat(parts[0].trim());
                        const lat = parseFloat(parts[1].trim());
                        if (!isNaN(lng) && !isNaN(lat)) {
                            coordinates.push([lng, lat]);
                            let storeInfo = {
                                originalIndex: allStoreData.length, // 在allStoreData中的索引
                                coords: [lng, lat],
                                storeId: storeIdIndex !== -1 ? row[storeIdIndex] : 'N/A',
                                storeName: storeNameIndex !== -1 ? row[storeNameIndex] : `门店 ${allStoreData.length + 1}`,
                                address: addressIndex !== -1 ? row[addressIndex] : ''
                            };
                            allStoreData.push(storeInfo);
                        } else { console.warn(`第 ${i+1} 行坐标格式无效: ${coordString}`); }
                    } else { console.warn(`第 ${i+1} 行坐标格式无法解析: ${coordString}`); }
                }
            }

            if (coordinates.length === 0) {
                alert("未能从文件中解析出任何有效的坐标数据！"); return;
            }
            if (coordinates.length < k) {
                alert(`门店数量 (${coordinates.length}) 少于分组数量 (${k})，请调整K值或检查数据。`); return;
            }

            // 创建 LabelMarker
            allStoreData.forEach((store, index) => {
                const icon = {
                    type: 'image',
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                    size: [10, 15],
                    anchor: 'bottom-center',
                };
                
                const labelMarker = new AMap.LabelMarker({
                    position: new AMap.LngLat(store.coords[0], store.coords[1]),
                    icon: icon,
                    rank: 100, // 多个LabelMarker层叠时，rank大的优先显示，可以用于高亮
                    extData: { // 存储额外信息
                        storeIndex: index, // 对应allStoreData的索引
                        groupIndex: -1,   // 初始未分组
                        defaultColor: defaultMarkerColor,
                        storeName: store.storeName,
                        storeId: store.storeId,
                        address: store.address
                    }
                });
                labelMarker.on('mouseover', handleMarkerMouseOver);
                labelMarker.on('mouseout', handleMarkerMouseOut);
                storeLabelMarkers.push(labelMarker);
            });
            
            // 一次性将海量点添加到图层
            labelsLayer.add(storeLabelMarkers);

            // 保存原始Excel数据以便导出
            originalExcelData = jsonData;
            originalHeaders = headers;
            
            // 设置全局距离计算方法
            useGeoDistanceGlobal = CONFIG.useGeoDistance;
            
            // 执行 K-means 聚类
            console.log(`开始聚类: K=${k}, 最大迭代=${CONFIG.maxIterations}, 使用地理距离=${CONFIG.useGeoDistance}`);
            const startTime = performance.now();
            currentClusterAssignments = kmeans(coordinates, k, CONFIG.maxIterations);
            const endTime = performance.now();
            console.log(`聚类完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
            
            updateMarkersAndDisplayResults(k);
        };
        reader.readAsArrayBuffer(file);
    }

    function updateMarkersAndDisplayResults(k) {
        // 更新LabelMarker的组信息和颜色
        currentClusterAssignments.forEach((clusterIdx, dataIdx) => {
            if (storeLabelMarkers[dataIdx]) {
                const groupColor = getGroupColor(clusterIdx);
                const extData = storeLabelMarkers[dataIdx].getExtData();
                
                // 更新扩展数据
                storeLabelMarkers[dataIdx].setExtData({
                    ...extData,
                    groupIndex: clusterIdx,
                    defaultColor: groupColor
                });
                
                // 使用默认的蓝色图标
                storeLabelMarkers[dataIdx].setIcon({
                    type: 'image',
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                    size: [10, 15],
                    anchor: 'bottom-center',
                });
            }
        });
        
        // 启用导出按钮
        document.getElementById('exportBtn').disabled = false;
        
        // 调整地图视野以包含所有点
        if (storeLabelMarkers.length > 0) {
            map.setFitView(storeLabelMarkers, false, [60, 100, 60, 350], 18); // 考虑右侧面板宽度留白
        }
    }
    
    // 根据分组索引获取对应的颜色代码（用于高德地图标记）
    function getColorCode(index) {
        // 高德地图内置的标记颜色代码：b-蓝, r-红, g-绿, o-橙, y-黄, p-紫, w-白, k-黑
        const colorCodes = ['b', 'r', 'g', 'o', 'y', 'p', 'w', 'k'];
        return colorCodes[index % colorCodes.length];
    }

    function handleMarkerMouseOver(e) {
        const currentMarker = e.target;
        const extData = currentMarker.getExtData();
        const currentGroupIndex = extData.groupIndex;
        const position = currentMarker.getPosition();
        
        // 显示信息窗口
        normalMarker.setContent(
            '<div class="amap-info-window">' + 
                extData.storeName + 
                (currentGroupIndex >= 0 ? ' (第' + (currentGroupIndex + 1) + '组)' : '') +
                '<div class="amap-info-sharp"></div>' + 
            '</div>'
        );
        normalMarker.setPosition(position);
        map.add(normalMarker);

        if (currentGroupIndex === -1) { // 未分组的点
            currentMarker.setRank(200); // 提高层级
            return;
        }

        // 高亮同组的点
        storeLabelMarkers.forEach(marker => {
            const markerExtData = marker.getExtData();
            if (markerExtData.groupIndex === currentGroupIndex) {
                // 使用红色标记高亮显示
                marker.setIcon({
                    type: 'image',
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                    size: [10, 15],
                    anchor: 'bottom-center',
                });
                marker.setRank(200); // 提高层级
            } else {
                marker.setRank(100); // 其他组恢复默认层级
            }
        });
    }

    function handleMarkerMouseOut(e) {
        const currentMarker = e.target;
        const extData = currentMarker.getExtData();
        const currentGroupIndex = extData.groupIndex;
        
        // 移除信息窗口
        map.remove(normalMarker);

        if (currentGroupIndex === -1) { // 未分组的点
            currentMarker.setRank(100);
            return;
        }

        // 恢复所有点为蓝色图标
        storeLabelMarkers.forEach(marker => {
            const markerExtData = marker.getExtData();
            if (markerExtData.groupIndex === currentGroupIndex) {
                // 恢复为蓝色图标
                marker.setIcon({
                    type: 'image',
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                    size: [10, 15],
                    anchor: 'bottom-center',
                });
            }
            marker.setRank(100); // 所有点恢复默认层级
        });
    }


    // K-means 算法实现 (与之前版本相同，此处省略以减少篇幅，请从上一版回答中复制过来)
    function kmeans(data, k, maxIterations = 100) {
        if (data.length === 0 || k === 0) return [];
        
        // 尝试找到地理上分散的初始中心点
        // 首先找到数据的地理边界
        let minLng = Infinity, maxLng = -Infinity, minLat = Infinity, maxLat = -Infinity;
        data.forEach(point => {
            minLng = Math.min(minLng, point[0]);
            maxLng = Math.max(maxLng, point[0]);
            minLat = Math.min(minLat, point[1]);
            maxLat = Math.max(maxLat, point[1]);
        });
        
        // 初始化质心 - 使用地理上分散的点
        let centroids = [];
        
        // 如果数据点较少，直接使用K-means++初始化
        if (data.length < 100 || k > 10) {
            // K-means++ 初始质心选择
            let initialCentroid = data[Math.floor(Math.random() * data.length)];
            centroids.push([...initialCentroid]);
            
            for (let i = 1; i < k; i++) {
                let distances = data.map(point => {
                    let minDist = Infinity;
                    centroids.forEach(centroid => {
                        const dist = calculateDistance(point, centroid);
                        if (dist < minDist) {
                            minDist = dist;
                        }
                    });
                    return minDist; // 不需要平方，因为Haversine已经返回实际距离
                });
                
                let sumDistances = distances.reduce((a, b) => a + b, 0);
                let randomValue = Math.random() * sumDistances;
                let cumulativeDistance = 0;
                let nextCentroidIndex = -1;
                
                for (let j = 0; j < distances.length; j++) {
                    cumulativeDistance += distances[j];
                    if (cumulativeDistance >= randomValue) {
                        nextCentroidIndex = j;
                        break;
                    }
                }
                if (nextCentroidIndex === -1) nextCentroidIndex = data.length - 1; // fallback
                centroids.push([...data[nextCentroidIndex]]);
            }
        } else {
            // 对于大量数据点，尝试使用地理网格初始化
            // 将地理区域划分为网格，从每个网格中选择一个点作为初始质心
            const gridSize = Math.ceil(Math.sqrt(k));
            const lngStep = (maxLng - minLng) / gridSize;
            const latStep = (maxLat - minLat) / gridSize;
            
            // 创建网格
            const grid = Array(gridSize).fill().map(() => Array(gridSize).fill([]));
            
            // 将点分配到网格
            data.forEach(point => {
                const lngIndex = Math.min(gridSize - 1, Math.floor((point[0] - minLng) / lngStep));
                const latIndex = Math.min(gridSize - 1, Math.floor((point[1] - minLat) / latStep));
                if (!grid[latIndex][lngIndex]) grid[latIndex][lngIndex] = [];
                grid[latIndex][lngIndex].push(point);
            });
            
            // 从每个非空网格中选择一个点
            let centroidCount = 0;
            for (let i = 0; i < gridSize && centroidCount < k; i++) {
                for (let j = 0; j < gridSize && centroidCount < k; j++) {
                    if (grid[i][j] && grid[i][j].length > 0) {
                        // 从该网格中随机选择一个点
                        const randomIndex = Math.floor(Math.random() * grid[i][j].length);
                        centroids.push([...grid[i][j][randomIndex]]);
                        centroidCount++;
                    }
                }
            }
            
            // 如果网格初始化没有提供足够的质心，使用K-means++补充
            if (centroids.length < k) {
                while (centroids.length < k) {
                    let distances = data.map(point => {
                        let minDist = Infinity;
                        centroids.forEach(centroid => {
                            const dist = calculateDistance(point, centroid);
                            if (dist < minDist) {
                                minDist = dist;
                            }
                        });
                        return minDist;
                    });
                    
                    let maxDist = -1;
                    let farthestPointIndex = 0;
                    distances.forEach((dist, idx) => {
                        if (dist > maxDist) {
                            maxDist = dist;
                            farthestPointIndex = idx;
                        }
                    });
                    
                    centroids.push([...data[farthestPointIndex]]);
                }
            }
        }
        
        // K-means 迭代
        let assignments = new Array(data.length);
        let oldAssignments;
        
        for (let iter = 0; iter < maxIterations; iter++) {
            oldAssignments = [...assignments];
            
            // 分配点到最近的质心
            for (let i = 0; i < data.length; i++) {
                let minDistance = Infinity;
                let closestCentroidIndex = -1;
                for (let j = 0; j < k; j++) {
                    const distance = calculateDistance(data[i], centroids[j]);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCentroidIndex = j;
                    }
                }
                assignments[i] = closestCentroidIndex;
            }
            
            // 更新质心位置
            const newCentroids = Array.from({ length: k }, () => [0, 0]);
            const counts = new Array(k).fill(0);
            for (let i = 0; i < data.length; i++) {
                const centroidIndex = assignments[i];
                if (typeof centroidIndex === 'number' && centroidIndex >= 0 && centroidIndex < k) {
                    newCentroids[centroidIndex][0] += data[i][0];
                    newCentroids[centroidIndex][1] += data[i][1];
                    counts[centroidIndex]++;
                }
            }
            
            // 计算新的质心位置
            for (let j = 0; j < k; j++) {
                if (counts[j] > 0) {
                    centroids[j][0] = newCentroids[j][0] / counts[j];
                    centroids[j][1] = newCentroids[j][1] / counts[j];
                } else {
                    // 如果一个质心没有分配到点，找一个最远的点作为新质心
                    let maxDist = -1;
                    let farthestPointIndex = Math.floor(Math.random() * data.length);
                    data.forEach((p, idx) => {
                        let minDistToCentroids = Infinity;
                        centroids.forEach(c => {
                            minDistToCentroids = Math.min(minDistToCentroids, calculateDistance(p, c));
                        });
                        if (minDistToCentroids > maxDist) {
                            maxDist = minDistToCentroids;
                            farthestPointIndex = idx;
                        }
                    });
                    centroids[j] = [...data[farthestPointIndex]];
                }
            }
            
            // 检查是否收敛
            let changed = false;
            for (let i = 0; i < data.length; i++) {
                if (assignments[i] !== oldAssignments[i]) {
                    changed = true;
                    break;
                }
            }
            if (!changed && iter > 0) break;
        }
        
        return assignments;
    }

    // 全局变量，用于控制距离计算方法
    let useGeoDistanceGlobal = true;
    
    // 导出分组结果到Excel
    function exportData() {
        if (!originalExcelData || !currentClusterAssignments || currentClusterAssignments.length === 0) {
            alert("没有可导出的数据，请先上传Excel文件并执行分组！");
            return;
        }
        
        try {
            // 复制原始数据
            const exportData = JSON.parse(JSON.stringify(originalExcelData));
            
            // 添加分组列
            const headers = exportData[0];
            headers.push("分组");
            
            // 为每行数据添加分组信息
            for (let i = 1; i < exportData.length; i++) {
                if (i-1 < currentClusterAssignments.length) {
                    const groupIndex = currentClusterAssignments[i-1];
                    exportData[i].push(groupIndex >= 0 ? `第${groupIndex + 1}组` : "未分组");
                } else {
                    exportData[i].push("未分组");
                }
            }
            
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(exportData);
            
            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, "分组结果");
            
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, "-").substring(0, 19);
            const fileName = `门店分组结果_${timestamp}.xlsx`;
            
            // 导出Excel文件
            XLSX.writeFile(wb, fileName);
            
            console.log(`导出成功: ${fileName}`);
        } catch (error) {
            console.error("导出数据时出错:", error);
            alert(`导出失败: ${error.message}`);
        }
    }
    
    function calculateDistance(point1, point2) {
        if (useGeoDistanceGlobal) {
            // 使用Haversine公式计算两个地理坐标点之间的距离
            const toRad = function(degree) {
                return degree * Math.PI / 180;
            };
            
            const R = 6371; // 地球半径，单位km
            const dLat = toRad(point2[1] - point1[1]);
            const dLon = toRad(point2[0] - point1[0]);
            const lat1 = toRad(point1[1]);
            const lat2 = toRad(point2[1]);
            
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.sin(dLon/2) * Math.sin(dLon/2) * Math.cos(lat1) * Math.cos(lat2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c; // 返回两点之间的距离，单位km
        } else {
            // 使用欧几里得距离（平面距离）
            const dx = point1[0] - point2[0];
            const dy = point1[1] - point2[1];
            return Math.sqrt(dx * dx + dy * dy); // 返回欧几里得距离
        }
    }

    // 不再需要displayListResults函数

</script>
</body>
</html>
