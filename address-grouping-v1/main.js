// 创建Vue应用
const { createApp, ref, reactive, onMounted, computed } = Vue;

// 全局变量，用于存储分组和标记物的引用
window.mapGroupResult = null;
window.mapMarkers = null;
window.mapProcessedData = null;

const app = createApp({
    setup() {
        // 响应式数据
        const fileList = ref([]);
        const hasFile = ref(false);
        const processing = ref(false);
        const progress = ref(0);
        const hasResult = ref(false);
        const form = reactive({
            groupCount: 10
        });
        
        // 存储数据
        const rawData = ref([]);
        const processedData = ref([]);
        const groupResult = ref([]);
        const groupSummary = ref([]);
        
        // 地图相关
        let map = null;
        const markers = ref([]);
        const polygons = ref([]);
        
        // 计算属性
        const progressFormat = (percentage) => {
            return processing.value ? `${percentage}%` : '';
        };
        
        // Web Worker
        let worker = null;
        
        // 初始化
        onMounted(() => {
            initMap();
            initWorker();
        });
        
        // 初始化地图
        const initMap = () => {
            map = new AMap.Map('map-container', {
                zoom: 11,
                center: [121.473667, 31.230525], // 上海中心
                viewMode: '3D'
            });
            
            // 添加地图控件
            map.addControl(new AMap.Scale());
            map.addControl(new AMap.ToolBar());
        };
        
        // 初始化Web Worker
        const initWorker = () => {
            if (window.Worker) {
                worker = new Worker('worker.js');
                
                worker.onmessage = (e) => {
                    const { type, payload } = e.data;
                    
                    if (type === 'progress') {
                        progress.value = payload.percentage;
                    } else if (type === 'result') {
                        progress.value = 100;
                        groupResult.value = payload.clusters;
                        renderResult();
                        processing.value = false;
                        hasResult.value = true;
                    }
                };
            } else {
                ElementPlus.ElMessage.error('您的浏览器不支持Web Worker，无法使用此功能');
            }
        };
        
        // 处理文件上传
        const handleFileChange = (file) => {
            if (file) {
                fileList.value = [file];
                hasFile.value = true;
                
                // 读取文件
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = e.target.result;
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        
                        // 转换为JSON
                        const jsonData = XLSX.utils.sheet_to_json(worksheet);
                        rawData.value = jsonData;
                        
                        // 处理数据
                        processedData.value = jsonData.map((item, index) => {
                            // 检查坐标格式
                            let coordinates = [0, 0];
                            if (item['坐标']) {
                                const coordParts = item['坐标'].split(',');
                                if (coordParts.length === 2) {
                                    coordinates = [parseFloat(coordParts[0]), parseFloat(coordParts[1])];
                                }
                            }
                            
                            return {
                                id: index,
                                storeId: item['门店号'] || '',
                                name: item['门店名称'] || '',
                                province: item['省份'] || '',
                                city: item['城市'] || '',
                                district: item['区'] || '',
                                address: item['详细地址'] || '',
                                coordinates,
                                rawData: item
                            };
                        });
                        
                        // 显示坐标点
                        showPoints();
                        
                        ElementPlus.ElMessage.success(`成功读取${processedData.value.length}条数据`);
                    } catch (error) {
                        console.error('解析Excel文件失败:', error);
                        ElementPlus.ElMessage.error('解析Excel文件失败，请检查文件格式');
                    }
                };
                
                reader.readAsArrayBuffer(file.raw);
            }
        };
        
        // 高亮显示组内的点
        const highlightGroup = (groupIndex) => {
            // 设置全局函数引用
            window.mapHighlightGroup = highlightGroup;
            window.mapResetHighlight = resetHighlight;
            
            if (!hasResult.value) return;
            
            // 找出该组内的所有点
            const groupPoints = groupResult.value.filter(p => p.cluster === groupIndex);
            const pointIds = groupPoints.map(p => p.id);
            
            // 更新标记颜色
            markers.value.forEach((marker, index) => {
                if (pointIds.includes(processedData.value[index].id)) {
                    // 设置为红色图标
                    marker.setIcon({
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                        size: [20, 20],
                        anchor: 'center'
                    });
                }
            });
        };
        
        // 重置高亮显示
        const resetHighlight = (groupIndex) => {
            if (!hasResult.value) return;
            
            // 找出该组内的所有点
            const groupPoints = groupResult.value.filter(p => p.cluster === groupIndex);
            const pointIds = groupPoints.map(p => p.id);
            
            // 恢复标记颜色
            markers.value.forEach((marker, index) => {
                if (pointIds.includes(processedData.value[index].id)) {
                    // 恢复为蓝色图标
                    marker.setIcon({
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                        size: [20, 20],
                        anchor: 'center'
                    });
                }
            });
        };
        
        // 显示坐标点
        const showPoints = () => {
            // 清除现有标记
            clearMarkers();
            
            // 创建标记图层
            const layer = new AMap.LabelsLayer({
                zooms: [3, 20],
                zIndex: 1000,
                collision: false
            });
            
            // 添加标记
            const labelMarkers = processedData.value.map(item => {
                const position = item.coordinates;
                
                // 创建标记 - 只使用默认图标，不显示门店名称
                const labelMarker = new AMap.LabelMarker({
                    position,
                    icon: {
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                        size: [20, 20],
                        anchor: 'center'
                    },
                    extData: {
                        id: item.id // 存储ID，用于后续查找
                    }
                });
                
                return labelMarker;
            });
            
            // 将标记添加到图层
            layer.add(labelMarkers);
            
            // 将图层添加到地图
            map.add(layer);
            
            // 保存标记引用
            markers.value = labelMarkers;
            
            // 调整地图视野
            if (labelMarkers.length > 0) {
                const bounds = getBounds(processedData.value.map(item => item.coordinates));
                map.setBounds(bounds);
            }
        };
        
        // 获取边界
        const getBounds = (positions) => {
            if (!positions || positions.length === 0) {
                return new AMap.Bounds([0, 0], [0, 0]);
            }
            
            let minLng = positions[0][0];
            let maxLng = positions[0][0];
            let minLat = positions[0][1];
            let maxLat = positions[0][1];
            
            positions.forEach(pos => {
                minLng = Math.min(minLng, pos[0]);
                maxLng = Math.max(maxLng, pos[0]);
                minLat = Math.min(minLat, pos[1]);
                maxLat = Math.max(maxLat, pos[1]);
            });
            
            return new AMap.Bounds([minLng, minLat], [maxLng, maxLat]);
        };
        
        // 清除标记
        const clearMarkers = () => {
            if (markers.value.length > 0) {
                map.remove(markers.value);
                markers.value = [];
            }
        };
        
        // 清除多边形
        const clearPolygons = () => {
            if (polygons.value.length > 0) {
                map.remove(polygons.value);
                polygons.value = [];
            }
        };
        
        // 处理数据
        const processData = () => {
            if (!hasFile.value || processedData.value.length === 0) {
                ElementPlus.ElMessage.warning('请先上传数据文件');
                return;
            }
            
            // 开始处理
            processing.value = true;
            progress.value = 0;
            hasResult.value = false;
            
            // 准备数据 - 确保只传递简单数据结构
            const pointsData = processedData.value.map(item => ({
                id: item.id,
                // 确保坐标是简单数组，不包含任何引用
                lng: item.coordinates[0],
                lat: item.coordinates[1]
            }));
            
            // 发送数据到Worker
            try {
                worker.postMessage({
                    type: 'process',
                    payload: {
                        points: pointsData,
                        k: form.groupCount
                    }
                });
            } catch (error) {
                console.error('发送数据到Worker失败:', error);
                ElementPlus.ElMessage.error('处理失败，请重试');
                processing.value = false;
            }
        };
        
        // 取消处理
        const cancelProcessing = () => {
            if (worker) {
                worker.postMessage({ type: 'cancel' });
                processing.value = false;
                progress.value = 0;
            }
        };
        
        // 渲染结果
        const renderResult = () => {
            // 清除现有多边形
            clearPolygons();
            
            // 生成颜色
            const colors = generateColors(form.groupCount);
            
            // 创建分组摘要
            groupSummary.value = Array.from({ length: form.groupCount }, (_, i) => {
                const groupPoints = groupResult.value.filter(p => p.cluster === i);
                return {
                    group: i + 1,
                    count: groupPoints.length,
                    color: colors[i]
                };
            });
            
            // 为每个分组创建多边形
            const newPolygons = [];
            
            for (let i = 0; i < form.groupCount; i++) {
                const groupPoints = groupResult.value.filter(p => p.cluster === i);
                
                if (groupPoints.length > 0) {
                    // 获取该组所有点的坐标
                    const positions = groupPoints.map(p => {
                        const point = processedData.value.find(d => d.id === p.id);
                        return point ? point.coordinates : null;
                    }).filter(Boolean);
                    
                    if (positions.length > 0) {
                        // 计算凸包或矩形边界
                        const bounds = getBounds(positions);
                        const rectangle = [
                            [bounds.getSouthWest().lng, bounds.getSouthWest().lat],
                            [bounds.getNorthEast().lng, bounds.getSouthWest().lat],
                            [bounds.getNorthEast().lng, bounds.getNorthEast().lat],
                            [bounds.getSouthWest().lng, bounds.getNorthEast().lat]
                        ];
                        
                        // 创建多边形
                        const polygon = new AMap.Polygon({
                            path: rectangle,
                            strokeColor: colors[i],
                            strokeWeight: 2,
                            strokeOpacity: 0.8,
                            fillColor: colors[i],
                            fillOpacity: 0.2,
                            zIndex: 50,
                            bubble: true,
                            extData: {
                                groupIndex: i // 存储组索引，用于鼠标交互
                            }
                        });
                        
                        // 添加鼠标事件 - 直接实现高亮和重置功能
                        polygon.on('mouseover', function() {
                            const groupIndex = this.getExtData().groupIndex;
                            
                            // 找出该组内的所有点
                            const groupPoints = groupResult.value.filter(p => p.cluster === groupIndex);
                            const pointIds = groupPoints.map(p => p.id);
                            
                            // 更新标记颜色
                            markers.value.forEach((marker, index) => {
                                if (pointIds.includes(processedData.value[index].id)) {
                                    // 设置为红色图标
                                    marker.setIcon({
                                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                                        size: [20, 20],
                                        anchor: 'center'
                                    });
                                }
                            });
                        });
                        
                        polygon.on('mouseout', function() {
                            const groupIndex = this.getExtData().groupIndex;
                            
                            // 找出该组内的所有点
                            const groupPoints = groupResult.value.filter(p => p.cluster === groupIndex);
                            const pointIds = groupPoints.map(p => p.id);
                            
                            // 恢复标记颜色
                            markers.value.forEach((marker, index) => {
                                if (pointIds.includes(processedData.value[index].id)) {
                                    // 恢复为蓝色图标
                                    marker.setIcon({
                                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                                        size: [20, 20],
                                        anchor: 'center'
                                    });
                                }
                            });
                        });
                        
                        // 添加标签
                        const center = bounds.getCenter();
                        const marker = new AMap.Marker({
                            position: [center.lng, center.lat],
                            content: `<div class="group-label">Group ${i + 1}</div>`,
                            zIndex: 100,
                            offset: new AMap.Pixel(-30, -30)
                        });
                        
                        newPolygons.push(polygon);
                        newPolygons.push(marker);
                    }
                }
            }
            
            // 添加到地图
            map.add(newPolygons);
            polygons.value = newPolygons;
            
            // 更新点的颜色
            updatePointColors(colors);
        };
        
        // 更新点的颜色
        const updatePointColors = (colors) => {
            // 这里可以根据分组结果更新点的颜色
            // 由于使用LabelMarker，可能需要重新创建标记
            showPoints();
        };
        
        // 生成颜色
        const generateColors = (count) => {
            const colors = [];
            const hueStep = 360 / count;
            
            for (let i = 0; i < count; i++) {
                const hue = i * hueStep;
                colors.push(`hsl(${hue}, 70%, 50%)`);
            }
            
            return colors;
        };
        
        // 导出数据
        const exportData = () => {
            if (!hasResult.value) {
                ElementPlus.ElMessage.warning('请先进行分组');
                return;
            }
            
            try {
                // 创建新的工作簿
                const wb = XLSX.utils.book_new();
                
                // 准备导出数据
                const exportData = rawData.value.map((item, index) => {
                    // 查找对应的处理后数据
                    const processedItem = processedData.value.find(p => p.rawData === item);
                    
                    if (!processedItem) return { ...item, '分组序号': '' };
                    
                    // 查找分组结果
                    const resultItem = groupResult.value.find(r => r.id === processedItem.id);
                    const groupNumber = resultItem ? resultItem.cluster + 1 : '';
                    
                    // 返回带分组的数据
                    return {
                        ...item,
                        '分组序号': groupNumber
                    };
                });
                
                // 创建工作表
                const ws = XLSX.utils.json_to_sheet(exportData);
                
                // 将工作表添加到工作簿
                XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
                
                // 生成Excel文件并下载
                XLSX.writeFile(wb, '地址分组结果.xlsx');
                
                ElementPlus.ElMessage.success('导出成功');
            } catch (error) {
                console.error('导出失败:', error);
                ElementPlus.ElMessage.error('导出失败，请重试');
            }
        };
        
        // 加载示例数据
        const loadSampleData = () => {
            if (typeof sampleData !== 'undefined') {
                // 清除现有文件
                fileList.value = [];
                
                // 设置数据
                rawData.value = sampleData;
                
                // 处理数据
                processedData.value = sampleData.map((item, index) => {
                    // 检查坐标格式
                    let coordinates = [0, 0];
                    if (item['坐标']) {
                        const coordParts = item['坐标'].split(',');
                        if (coordParts.length === 2) {
                            coordinates = [parseFloat(coordParts[0]), parseFloat(coordParts[1])];
                        }
                    }
                    
                    return {
                        id: index,
                        storeId: item['门店号'] || '',
                        name: item['门店名称'] || '',
                        province: item['省份'] || '',
                        city: item['城市'] || '',
                        district: item['区'] || '',
                        address: item['详细地址'] || '',
                        coordinates,
                        rawData: item
                    };
                });
                
                // 显示坐标点
                showPoints();
                
                // 设置状态
                hasFile.value = true;
                
                ElementPlus.ElMessage.success(`成功加载${processedData.value.length}条示例数据`);
            } else {
                ElementPlus.ElMessage.error('示例数据未找到');
            }
        };
        
        return {
            fileList,
            hasFile,
            processing,
            progress,
            progressFormat,
            hasResult,
            form,
            groupSummary,
            handleFileChange,
            processData,
            cancelProcessing,
            exportData,
            loadSampleData
        };
    }
});

// 注册ElementPlus
app.use(ElementPlus);

// 注册ElementPlus图标
if (window.ElementPlusIconsVue) {
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component);
    }
}

// 挂载应用
app.mount('#app');
