<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址坐标分组工具</title>
    <!-- 引入Vue3 -->
    <script src="https://smart-static.wosaimg.com/libs/vue/3.3.4/vue.global.min.js"></script>
    <!-- 引入ElementPlus -->
    <link rel="stylesheet" href="https://smart-static.wosaimg.com/libs/element-plus/2.3.4/index.min.css">
    <script src="https://smart-static.wosaimg.com/libs/element-plus/2.3.4/index.full.min.js"></script>
    <!-- 引入ElementPlus图标 -->
    <script src="https://smart-static.wosaimg.com/libs/element-plus-icons-vue/2.1.0/index.min.js"></script>
    <!-- 引入xlsx库 -->
    <script src="https://smart-static.wosaimg.com/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- 引入高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=ed8ae3c141ca2df607dd0fe368e4a189&plugin=AMap.Scale,AMap.ToolBar"></script>
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <!-- 地图容器 -->
        <div id="map-container"></div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <el-card shadow="always">
                <template #header>
                    <div class="card-header">
                        <h3>地址坐标分组工具</h3>
                    </div>
                </template>
                
                <!-- 文件上传 -->
                <el-upload
                    class="upload-demo"
                    drag
                    action="#"
                    :auto-upload="false"
                    :on-change="handleFileChange"
                    :limit="1"
                    :file-list="fileList">
                    <el-icon class="el-icon--upload"><el-icon-upload /></el-icon>
                    <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip">
                            请上传包含门店信息的Excel文件，表头：门店号、门店名称、省份、城市、区、详细地址、坐标
                        </div>
                    </template>
                </el-upload>
                
                <!-- 分组设置 -->
                <div class="form-item">
                    <el-form :model="form" label-width="80px">
                        <el-form-item label="分组数量">
                            <el-input-number v-model="form.groupCount" :min="1" :max="100" />
                        </el-form-item>
                        
                        <el-form-item>
                            <el-button type="primary" @click="processData" :disabled="!hasFile || processing">开始分组</el-button>
                            <el-button @click="exportData" :disabled="!hasResult">导出结果</el-button>
                            <el-button @click="loadSampleData" type="info">加载示例数据</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                
                <!-- 进度条 -->
                <div v-if="processing" class="progress-container">
                    <el-progress :percentage="progress" :format="progressFormat"></el-progress>
                    <el-button size="small" @click="cancelProcessing" style="margin-top: 10px;">取消</el-button>
                </div>
                
                <!-- 结果统计 -->
                <div v-if="hasResult" class="result-info">
                    <h4>分组结果</h4>
                    <el-table :data="groupSummary" style="width: 100%" size="small">
                        <el-table-column prop="group" label="分组" width="60" />
                        <el-table-column prop="count" label="数量" width="60" />
                        <el-table-column prop="color" label="颜色">
                            <template #default="scope">
                                <div class="color-block" :style="{backgroundColor: scope.row.color}"></div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-card>
        </div>
    </div>
    
    <!-- 引入示例数据 -->
    <script src="sample-data.js"></script>
    <!-- 引入主脚本 -->
    <script src="main.js"></script>
</body>
</html>
