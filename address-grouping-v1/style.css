html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

#app {
    height: 100%;
    width: 100%;
    position: relative;
}

#map-container {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.control-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 350px;
    z-index: 10;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-item {
    margin-top: 15px;
}

.progress-container {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    background-color: #f5f7fa;
}

.result-info {
    margin-top: 15px;
}

.color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-block;
}

/* 确保ElementPlus图标正常显示 */
.el-icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* 自定义地图标记样式 */
.amap-marker-label {
    border: none;
    background-color: transparent;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .control-panel {
        width: 90%;
        left: 5%;
        right: 5%;
        top: 10px;
    }
}
