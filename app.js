// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成');

    // 创建Vue应用
    const app = Vue.createApp({
        data() {
            return {
                map: null,
                geocoder: null,
                labelsLayer: null,
                addresses: [], // 原始地址数据
                coordinates: [], // 坐标数据
                clusterCount: 10, // 默认分组数量
                clusters: [], // 聚类结果
                processing: false, // 处理状态
                processingMessage: '准备就绪', // 处理状态消息
                markers: [], // 地图标记
                rectangles: [], // 矩形标记
                initError: null // 初始化错误信息
            };
        },
        mounted() {
            // 初始化地图
            console.log('Vue应用已挂载，开始初始化地图');
            setTimeout(() => {
                try {
                    this.initMap();
                    console.log('地图初始化成功');
                } catch (error) {
                    console.error('地图初始化失败:', error);
                    this.initError = error.message;
                    this.processingMessage = '地图初始化失败: ' + error.message;
                    this.processing = true;
                }
            }, 500); // 延迟初始化地图，确保DOM已经渲染
        },
    methods: {
        // 初始化地图
        initMap() {
            console.log('开始初始化地图');

            if (typeof AMap === 'undefined') {
                throw new Error('高德地图API未加载，请检查网络连接');
            }

            console.log('创建地图实例');
            this.map = new AMap.Map('map', {
                zoom: 10,
                center: [116.397428, 39.90923],
                viewMode: '2D'
            });

            console.log('添加地图控件');
            // 添加地图控件
            this.map.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
                this.map.addControl(new AMap.ToolBar());
                this.map.addControl(new AMap.Scale());
                console.log('地图控件添加成功');
            });

            console.log('初始化地理编码服务');
            // 初始化地理编码服务
            this.geocoder = new AMap.Geocoder({
                city: "全国", // 城市，默认全国
                batch: true // 是否批量查询
            });

            console.log('初始化标签图层');
            // 初始化标签图层
            this.labelsLayer = new AMap.LabelsLayer({
                zooms: [3, 20],
                zIndex: 1000,
                collision: true,
                allowCollision: true
            });

            this.map.add(this.labelsLayer);
            console.log('地图初始化完成');
        },

        // 处理文件上传
        handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    // 获取第一个工作表
                    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

                    // 转换为JSON
                    const jsonData = XLSX.utils.sheet_to_json(firstSheet);

                    // 检查数据格式
                    if (jsonData.length === 0) {
                        alert('Excel文件中没有数据');
                        return;
                    }

                    // 检查必要的列
                    const firstRow = jsonData[0];
                    const requiredColumns = ['门店号', '门店名称', '省份', '城市', '区', '详细地址'];
                    const missingColumns = requiredColumns.filter(col => !(col in firstRow));

                    if (missingColumns.length > 0) {
                        alert(`Excel文件缺少必要的列: ${missingColumns.join(', ')}`);
                        return;
                    }

                    // 保存地址数据
                    this.addresses = jsonData;

                    // 清除之前的标记
                    this.clearMapMarkers();

                } catch (error) {
                    console.error('解析Excel文件出错:', error);
                    alert('解析Excel文件出错，请检查文件格式');
                }
            };

            reader.readAsArrayBuffer(file);
        },

        // 处理地址数据
        async processAddresses() {
            if (this.addresses.length === 0) {
                alert('请先上传地址数据');
                return;
            }

            this.processing = true;
            this.processingMessage = '正在处理地址数据...';

            try {
                // 清除之前的标记
                this.clearMapMarkers();

                // 地址转坐标
                this.processingMessage = '正在将地址转换为坐标...';
                await this.geocodeAddresses();

                // 执行聚类
                this.processingMessage = '正在进行坐标聚类...';
                await this.clusterCoordinates();

                // 显示结果
                this.processingMessage = '正在显示结果...';
                this.displayResults();

                // 调整地图视野以包含所有点
                this.map.setFitView();

            } catch (error) {
                console.error('处理地址数据出错:', error);
                alert('处理地址数据出错: ' + error.message);
            } finally {
                this.processing = false;
            }
        },

        // 地址转坐标
        async geocodeAddresses() {
            this.coordinates = [];

            // 分批处理，每批10个地址
            const batchSize = 10;
            const batches = Math.ceil(this.addresses.length / batchSize);

            for (let i = 0; i < batches; i++) {
                const start = i * batchSize;
                const end = Math.min(start + batchSize, this.addresses.length);
                const batch = this.addresses.slice(start, end);

                this.processingMessage = `正在转换坐标 (${start + 1}-${end}/${this.addresses.length})...`;

                // 创建地址字符串数组
                const addressStrings = batch.map(item => {
                    return `${item.省份}${item.城市}${item.区}${item.详细地址}`;
                });

                // 批量地理编码
                await new Promise((resolve, reject) => {
                    this.geocoder.getLocation(addressStrings, (status, result) => {
                        if (status === 'complete' && result.info === 'OK') {
                            // 处理结果
                            for (let j = 0; j < result.geocodes.length; j++) {
                                const geocode = result.geocodes[j];
                                if (geocode && geocode.location) {
                                    const index = start + j;
                                    if (index < this.addresses.length) {
                                        this.coordinates.push({
                                            index: index,
                                            lnglat: [geocode.location.lng, geocode.location.lat],
                                            address: this.addresses[index]
                                        });
                                    }
                                }
                            }
                            resolve();
                        } else {
                            reject(new Error('地理编码失败: ' + result.info));
                        }
                    });
                });

                // 添加延迟，避免API调用过于频繁
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            if (this.coordinates.length === 0) {
                throw new Error('没有成功转换的坐标');
            }
        },

        // 执行坐标聚类
        async clusterCoordinates() {
            return new Promise((resolve) => {
                // 提取坐标点
                const points = this.coordinates.map(coord => coord.lnglat);

                // 创建聚类实例
                const kmeans = new KMeansClustering(this.clusterCount);

                // 执行聚类
                const result = kmeans.cluster(points);

                // 保存聚类结果
                this.clusters = result.clusters;

                // 为每个地址分配聚类
                for (let i = 0; i < this.coordinates.length; i++) {
                    const coord = this.coordinates[i];
                    const clusterIndex = result.assignments[i];
                    coord.cluster = clusterIndex;
                }

                resolve();
            });
        },

        // 显示结果
        displayResults() {
            // 创建标记
            this.createMarkers();

            // 创建矩形
            this.createRectangles();
        },

        // 创建标记
        createMarkers() {
            // 清除之前的标记
            this.labelsLayer.clear();
            this.markers = [];

            // 为每个坐标创建标记
            this.coordinates.forEach((coord, index) => {
                const clusterIndex = coord.cluster;

                // 设置图标
                const icon = {
                    type: 'image',
                    image: 'https://a.amap.com/jsapi_demos/static/demo-center/marker/marker.png',
                    size: [25, 34],
                    anchor: 'bottom-center'
                };

                // 设置文本
                const text = {
                    content: coord.address.门店名称,
                    direction: 'right',
                    offset: [0, -5],
                    style: {
                        fontSize: 12,
                        fillColor: '#333',
                        strokeColor: '#fff',
                        strokeWidth: 2
                    }
                };

                // 创建标记
                const marker = new AMap.LabelMarker({
                    position: coord.lnglat,
                    icon: icon,
                    text: text,
                    rank: 1,
                    zIndex: 10,
                    extData: {
                        index: index,
                        cluster: clusterIndex,
                        address: coord.address
                    }
                });

                // 添加点击事件
                marker.on('click', () => {
                    const extData = marker.getExtData();
                    const content = `
                        <div style="padding: 10px;">
                            <h3>${extData.address.门店名称}</h3>
                            <p>门店号: ${extData.address.门店号}</p>
                            <p>地址: ${extData.address.省份}${extData.address.城市}${extData.address.区}${extData.address.详细地址}</p>
                            <p>分组: ${extData.cluster + 1}</p>
                        </div>
                    `;

                    const infoWindow = new AMap.InfoWindow({
                        content: content,
                        offset: new AMap.Pixel(0, -34)
                    });

                    infoWindow.open(this.map, marker.getPosition());
                });

                this.markers.push(marker);
            });

            // 批量添加到图层
            this.labelsLayer.add(this.markers);
        },

        // 创建矩形
        createRectangles() {
            // 清除之前的矩形
            this.rectangles.forEach(rect => {
                this.map.remove(rect);
            });
            this.rectangles = [];

            // 为每个聚类创建矩形
            this.clusters.forEach((cluster, index) => {
                if (cluster.points.length === 0) return;

                // 创建矩形
                const rectangle = new AMap.Rectangle({
                    bounds: new AMap.Bounds(
                        cluster.bounds.min,
                        cluster.bounds.max
                    ),
                    strokeColor: this.getClusterColor(index),
                    strokeWeight: 2,
                    strokeOpacity: 0.8,
                    fillColor: this.getClusterColor(index),
                    fillOpacity: 0.2,
                    zIndex: 5,
                    extData: {
                        clusterIndex: index
                    }
                });

                // 添加标签
                const center = [
                    (cluster.bounds.min[0] + cluster.bounds.max[0]) / 2,
                    (cluster.bounds.min[1] + cluster.bounds.max[1]) / 2
                ];

                const text = new AMap.Text({
                    text: `组 ${index + 1} (${cluster.points.length}个)`,
                    position: center,
                    style: {
                        'background-color': this.getClusterColor(index),
                        'border-width': '1px',
                        'border-color': '#fff',
                        'color': '#fff',
                        'font-size': '12px',
                        'padding': '2px 5px',
                        'border-radius': '3px'
                    },
                    zIndex: 6
                });

                this.map.add([rectangle, text]);
                this.rectangles.push(rectangle, text);
            });
        },

        // 获取聚类颜色
        getClusterColor(index) {
            const colors = [
                '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
                '#1abc9c', '#d35400', '#34495e', '#16a085', '#c0392b',
                '#2980b9', '#27ae60', '#e67e22', '#8e44ad', '#f1c40f'
            ];

            return colors[index % colors.length];
        },

        // 清除地图标记
        clearMapMarkers() {
            // 清除标记
            this.labelsLayer.clear();
            this.markers = [];

            // 清除矩形
            this.rectangles.forEach(rect => {
                this.map.remove(rect);
            });
            this.rectangles = [];

            // 清除聚类结果
            this.clusters = [];
        },

        // 导出Excel
        exportExcel() {
            if (this.addresses.length === 0 || this.coordinates.length === 0) {
                alert('没有数据可导出');
                return;
            }

            try {
                // 创建工作表数据
                const wsData = this.addresses.map((address, index) => {
                    // 查找对应的坐标数据
                    const coordData = this.coordinates.find(c => c.index === index);
                    const clusterIndex = coordData ? coordData.cluster + 1 : '';

                    // 复制原始数据并添加分组信息
                    return {
                        ...address,
                        '分组': clusterIndex,
                        '经度': coordData ? coordData.lnglat[0] : '',
                        '纬度': coordData ? coordData.lnglat[1] : ''
                    };
                });

                // 创建工作表
                const ws = XLSX.utils.json_to_sheet(wsData);

                // 创建工作簿
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, '地址分组结果');

                // 导出文件
                XLSX.writeFile(wb, '地址分组结果.xlsx');

            } catch (error) {
                console.error('导出Excel出错:', error);
                alert('导出Excel出错: ' + error.message);
            }
        }
    }
});

    // 挂载Vue应用
    try {
        console.log('准备挂载Vue应用');
        app.mount('#app');
        console.log('Vue应用挂载成功');
    } catch (error) {
        console.error('Vue应用挂载失败:', error);
    }
});
