/**
 * detectRiskFromWindows (优化版本 - 增加扩展持续性检测和趋势偏离检测)
 *
 * 输入参数:
 *  - currentWindow: 数字数组 或 包含 {time, value} 对象的数组 (最新->最旧 或 最旧->最新 都可以)
 *  - yoyWindow:     同长度的数组，格式相同 (同比窗口)
 *  - config:        可选配置参数 (见默认值)
 *
 * 输出结果: {
 *   risk: float 0..1, // 风险概率，可直接用作百分比展示
 *   raw: number,      // 原始评分
 *   S: severity (0..5),        // 严重性
 *   M: magnitude (0..2),       // 幅度
 *   P: persistence (0..3),     // 原始持续性评分
 *   EP: extendedPersistence (0..5), // 扩展持续性评分
 *   TD: trendDeviation (boolean),   // 趋势偏离检测
 *   consCount: 窗口末尾连续异常数量,
 *   trendBias: 趋势偏离详情,
 *   perPoint: [{time, C, Y, B, r, z, ratio, isAnomaly}, ...],
 *   a, b,
 *   details: {medianSigma, cusumTriggered, trendAnalysis, ...}
 * }
 */

function detectRiskFromWindows(currentWindow, yoyWindow, config = {}) {
    // ---------- 默认配置 ----------
    const cfg = {
        ewmaSpan: config.ewmaSpan ?? 3,
        madWindow: config.madWindow ?? 7,
        beta: config.beta ?? 0.02,
        z_th: config.z_th ?? 2.5,
        ratio_th: config.ratio_th ?? 0.12,
        tau: config.tau ?? 3,
        gamma: config.gamma ?? 0.5,
        L: config.L ?? 3, // 原始连续分钟数阈值
        enableCUSUM: config.enableCUSUM ?? true,
        eps: 1e-9,

        // 新增配置
        targetConsecutiveMinutes: config.targetConsecutiveMinutes ?? 5, // 目标连续异常分钟数
        trendDeviationWindow: config.trendDeviationWindow ?? 10, // 趋势偏离检测窗口
        trendBiasThreshold: config.trendBiasThreshold ?? 0.8, // 80%同向偏离阈值
        extendedPersistenceWeight: config.extendedPersistenceWeight ?? 1.5, // 扩展持续性权重
    };

    // ---------- 工具函数 ----------
    const toSeries = (arr) => {
        if (!Array.isArray(arr)) return { times: [], vals: [] };
        if (arr.length === 0) return { times: [], vals: [] };
        const times = [];
        const vals = [];
        for (let i = 0; i < arr.length; i++) {
            const it = arr[i];
            if (it == null) continue;
            if (typeof it === 'number') {
                times.push(i);
                vals.push(it);
            } else if (typeof it === 'object') {
                // 接受 {time, value} 或 {t, v} 格式
                const v = it.value ?? it.val ?? it.v ?? it.count ?? it.y ?? it;
                const t = it.time ?? it.t ?? i;
                const n = Number(v);
                if (!Number.isFinite(n)) {
                    times.push(t);
                    vals.push(0);
                } else {
                    times.push(t);
                    vals.push(n);
                }
            } else if (typeof it === 'string') {
                const n = Number(it);
                times.push(i);
                vals.push(Number.isFinite(n) ? n : 0);
            }
        }
        return { times, vals };
    };

    const median = (arr) => {
        if (!arr || arr.length === 0) return 0;
        const a = Array.from(arr).sort((x, y) => x - y);
        const m = Math.floor(a.length / 2);
        return a.length % 2 === 1 ? a[m] : (a[m - 1] + a[m]) / 2;
    };

    const mad = (arr) => {
        if (!arr || arr.length === 0) return 0;
        const med = median(arr);
        const devs = arr.map((x) => Math.abs(x - med));
        return median(devs);
    };

    const ewma = (vals, span) => {
        if (!vals || vals.length === 0) return [];
        const s = [];
        const alpha = 2 / (Math.max(1, span) + 1);
        let agg = vals[0] ?? 0;
        s[0] = agg;
        for (let i = 1; i < vals.length; i++) {
            agg = alpha * vals[i] + (1 - alpha) * agg;
            s[i] = agg;
        }
        return s;
    };

    const rollingSlice = (arr, idx, w) => {
        const start = Math.max(0, idx - w + 1);
        return arr.slice(start, idx + 1);
    };

    // ---------- 新增：扩展持续性检测函数 ----------
    const getExtendedPersistence = (perPoint, targetMinutes) => {
        let consecutiveCount = 0;
        for (let i = perPoint.length - 1; i >= 0; i--) {
            if (perPoint[i].isAnomaly) {
                consecutiveCount += 1;
            } else {
                break;
            }
        }

        // 更细粒度的持续性评分
        if (consecutiveCount >= targetMinutes) return 5;
        if (consecutiveCount >= Math.ceil(targetMinutes * 0.8)) return 4;
        if (consecutiveCount >= Math.ceil(targetMinutes * 0.6)) return 3;
        if (consecutiveCount >= Math.ceil(targetMinutes * 0.4)) return 2;
        if (consecutiveCount >= 1) return 1;
        return 0;
    };

    // ---------- 新增：趋势偏离检测函数 ----------
    const detectTrendDeviation = (perPoint, windowSize, biasThreshold) => {
        if (perPoint.length < windowSize) {
            windowSize = perPoint.length;
        }

        const recentPoints = perPoint.slice(-windowSize);
        const recentResiduals = recentPoints.map(p => p.r);

        // 计算正向和负向偏离的比例
        const positiveCount = recentResiduals.filter(r => r > 0).length;
        const negativeCount = recentResiduals.filter(r => r < 0).length;
        const zeroCount = recentResiduals.filter(r => r === 0).length;

        const totalCount = recentResiduals.length;
        const positiveBias = positiveCount / totalCount;
        const negativeBias = negativeCount / totalCount;
        const maxBias = Math.max(positiveBias, negativeBias);

        // 计算偏离强度
        const avgAbsResidual = recentResiduals.reduce((sum, r) => sum + Math.abs(r), 0) / totalCount;
        const avgPositiveResidual = recentResiduals.filter(r => r > 0).reduce((sum, r) => sum + r, 0) / (positiveCount || 1);
        const avgNegativeResidual = recentResiduals.filter(r => r < 0).reduce((sum, r) => sum + Math.abs(r), 0) / (negativeCount || 1);

        const isDeviation = maxBias >= biasThreshold;
        const direction = positiveBias > negativeBias ? 'UP' : (negativeBias > positiveBias ? 'DOWN' : 'NEUTRAL');

        return {
            isDeviation,
            direction,
            bias: maxBias,
            positiveBias,
            negativeBias,
            avgAbsResidual,
            avgPositiveResidual,
            avgNegativeResidual,
            windowSize,
            positiveCount,
            negativeCount,
            zeroCount
        };
    };

    // ---------- 输入标准化 ----------
    const cur = toSeries(currentWindow);
    const yo = toSeries(yoyWindow);
    const N = Math.min(cur.vals.length, yo.vals.length);
    if (N <= 0) {
        return { risk: 0.01, raw: 0, reason: 'empty_input' };
    }

    const Craw = cur.vals.slice(0, N);
    const Yraw = yo.vals.slice(0, N);
    const times = cur.times.slice(0, N);

    // ---------- 平滑处理 ----------
    const C = ewma(Craw, cfg.ewmaSpan);
    const Y = ewma(Yraw, cfg.ewmaSpan);

    // ---------- 鲁棒仿射校准 a, b ----------
    const eps = cfg.eps;
    const ratios = [];
    const diffs = [];
    for (let i = 0; i < N; i++) {
        const denom = (Y[i] ?? 0) + eps;
        ratios.push((C[i] ?? 0) / denom);
    }
    const a = median(ratios);
    for (let i = 0; i < N; i++) {
        diffs.push((C[i] ?? 0) - a * (Y[i] ?? 0));
    }
    const b = median(diffs);

    // ---------- 基线和残差 ----------
    const B = new Array(N);
    const R = new Array(N);
    for (let i = 0; i < N; i++) {
        B[i] = a * (Y[i] ?? 0) + b;
        R[i] = (C[i] ?? 0) - B[i];
    }

    // ---------- 计算每个点的sigma、z、ratio、isAnomaly ----------
    const perPoint = [];
    const w = Math.max(1, cfg.madWindow);
    const sigmas = new Array(N).fill(0);
    for (let i = 0; i < N; i++) {
        const Ywin = rollingSlice(Y, i, w);
        const madY = mad(Ywin);
        const sigmaY = 1.4826 * madY;
        const Rwin = rollingSlice(R, i, w);
        const madR = mad(Rwin);
        const sigmaR = 1.4826 * madR;
        const sigma_i = Math.sqrt((a * sigmaY) ** 2 + sigmaR ** 2) + cfg.beta * Math.max(B[i], 0);
        sigmas[i] = sigma_i;

        // 斜率放松因子 kappa
        let db = 0;
        if (i >= 1) db = B[i] - B[i - 1];
        // 收集最近的绝对斜率
        const absSlopes = [];
        for (let j = Math.max(1, i - w + 1); j <= i; j++) {
            absSlopes.push(Math.abs(B[j] - B[j - 1] || 0));
        }
        const medianAbsSlope = Math.max(median(absSlopes), eps);
        const kappa = 1 + cfg.gamma * (Math.abs(db) / medianAbsSlope);
        const T = cfg.tau * kappa * (sigma_i + eps);

        const r = R[i];
        const z = r / (sigma_i + eps);
        const ratio = Math.abs(r) / Math.max(B[i], 1); // 避免除零

        const isAnom = Math.abs(z) >= cfg.z_th || ratio >= cfg.ratio_th;
        perPoint.push({
            time: times[i],
            index: i,
            C: C[i],
            Y: Y[i],
            B: B[i],
            r,
            sigma: sigma_i,
            threshold: T,
            z,
            ratio,
            isAnomaly: Boolean(isAnom),
            madY: madY,
            madR: madR,
            kappa,
        });
    }

    // ---------- 原始窗口末尾连续异常 ----------
    let consCount = 0;
    for (let i = N - 1; i >= 0; i--) {
        if (perPoint[i].isAnomaly) consCount += 1;
        else break;
    }
    // 限制持续性评分
    const P = Math.min(3, consCount);

    // ---------- 新增：扩展持续性检测 ----------
    const EP = getExtendedPersistence(perPoint, cfg.targetConsecutiveMinutes);

    // ---------- 新增：趋势偏离检测 ----------
    const trendAnalysis = detectTrendDeviation(
        perPoint,
        cfg.trendDeviationWindow,
        cfg.trendBiasThreshold
    );
    const TD = trendAnalysis.isDeviation;

    // ---------- 计算最后一个点的严重性和幅度 ----------
    const last = perPoint[N - 1];
    const z_clip = Math.min(Math.max(Math.abs(last.z), 0), 6);
    let S = 0;
    if (z_clip > 1) S = 5 * ((z_clip - 1) / 5); // 将 z 在 (1..6) 线性映射到 S (0..5)
    let M = 0;
    if (last.ratio >= 0.5) M = 2;
    else if (last.ratio >= 0.2) M = 1;

    // ---------- 可选的 CUSUM 检测残差缓慢漂移 ----------
    let cusumTriggered = false;
    let cusumBonus = 0;
    if (cfg.enableCUSUM) {
        // 使用中位数sigma设置 v 和 h
        const medianSigma = median(sigmas) || 1;
        const v = 0.25 * medianSigma;
        const h = 5 * medianSigma;
        let Spos = 0, Sneg = 0;
        for (let i = 0; i < N; i++) {
            const r = R[i];
            Spos = Math.max(0, Spos + r - v);
            Sneg = Math.max(0, Sneg - r - v);
            if (Spos > h || Sneg > h) {
                cusumTriggered = true;
                break;
            }
        }
        if (cusumTriggered) cusumBonus = 1;
    }

    // ---------- 增强的最终评分计算 ----------
    // 基础分数
    let raw = S + M + P + cusumBonus;

    // 扩展持续性加分
    const extendedPersistenceBonus = EP > P ? (EP - P) * cfg.extendedPersistenceWeight : 0;

    // 趋势偏离加分
    const trendDeviationBonus = TD ? (trendAnalysis.bias >= 0.9 ? 2 : 1) : 0;

    // 复合异常加分（持续异常 + 趋势偏离）
    const compoundBonus = (EP >= 4 && TD && S >= 2) ? 1 : 0;

    raw += extendedPersistenceBonus + trendDeviationBonus + compoundBonus;

    // ---------- 映射到0-1范围的风险概率 ----------
    // 理论最大值：S(5) + M(2) + P(3) + CUSUM(1) + EP_bonus(3*1.5=4.5) + TD_bonus(2) + compound(1) = 18.5
    // 实际常见最大值约为12-15，使用sigmoid函数进行平滑映射
    const maxExpectedRaw = 12;

    // 使用sigmoid函数将raw分数映射到0-1概率
    // risk = 1 / (1 + exp(-k * (raw - threshold)))
    const k = 0.8;  // 控制曲线陡峭度
    const threshold = maxExpectedRaw * 0.5;  // 50%概率对应的raw值

    let risk = 1 / (1 + Math.exp(-k * (raw - threshold)));

    // 确保最小值不低于1%（方便看板显示）
    if (risk < 0.01) risk = 0.01;
    // 确保最大值不超过99%（避免100%的绝对判断）
    if (risk > 0.99) risk = 0.99;

    // 保留3位小数，适合百分比显示
    risk = Math.round(risk * 1000) / 1000;

    return {
        risk,
        raw,
        S,
        M,
        P,
        EP,  // 扩展持续性评分
        TD,  // 趋势偏离标志
        consCount,
        a,
        b,
        perPoint,
        trendBias: trendAnalysis,  // 趋势偏离详细信息
        details: {
            medianSigma: median(sigmas),
            cusumTriggered,
            windowN: N,
            config: cfg,
            extendedPersistenceBonus,
            trendDeviationBonus,
            compoundBonus,
            trendAnalysis,
            riskMappingInfo: {
                rawScore: raw,
                maxExpectedRaw: 12,
                sigmoidThreshold: 6,
                finalRiskProbability: risk,
                sigmoidK: k,
                sigmoidThreshold: threshold
            }
        },
    };
}

// ---------- 新增：智能告警策略函数 ----------
function intelligentAlerting(result) {
    const alerts = [];
    const { risk, S, EP, TD, trendBias, consCount } = result;

    // 复合异常 - 最高优先级
    if (EP >= 5 && TD && S >= 3) {
        alerts.push({
            level: 'CRITICAL',
            type: 'COMPOUND_ANOMALY',
            message: `检测到严重复合异常：连续${consCount}分钟异常，趋势${trendBias.direction}向偏离${(trendBias.bias * 100).toFixed(1)}%`,
            priority: 1,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }
    // 严重持续异常
    else if (EP >= 5) {
        alerts.push({
            level: 'WARNING',
            type: 'PERSISTENT_ANOMALY',
            message: `连续${consCount}分钟异常，建议立即检查`,
            priority: 2,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }
    // 趋势性异常
    else if (TD && trendBias.bias >= 0.9) {
        alerts.push({
            level: 'WARNING',
            type: 'STRONG_TREND_DEVIATION',
            message: `检测到强趋势偏离：${(trendBias.bias * 100).toFixed(1)}%的时间点${trendBias.direction}向异常`,
            priority: 2,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }
    // 一般趋势偏离
    else if (TD) {
        alerts.push({
            level: 'INFO',
            type: 'TREND_DEVIATION',
            message: `检测到趋势性偏离：${trendBias.direction}向偏离${(trendBias.bias * 100).toFixed(1)}%`,
            priority: 3,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }
    // 中等持续异常
    else if (EP >= 3) {
        alerts.push({
            level: 'INFO',
            type: 'MEDIUM_PERSISTENT',
            message: `连续${consCount}分钟异常，请关注`,
            priority: 3,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }
    // 高风险单点异常
    else if (risk >= 0.8) {
        alerts.push({
            level: 'WARNING',
            type: 'HIGH_RISK_SPIKE',
            message: `检测到高风险异常点，风险概率${(risk * 100).toFixed(1)}%`,
            priority: 2,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }
    // 通用低风险提醒
    else if (risk >= 0.2) {
        alerts.push({
            level: 'INFO',
            type: 'LOW_RISK',
            message: `检测到轻微异常，风险概率${(risk * 100).toFixed(1)}%`,
            priority: 4,
            riskProbability: risk,
            riskPercentage: `${(risk * 100).toFixed(1)}%`
        });
    }

    return alerts;
}

// ---------- 使用示例 ----------
/*
// 基础用法
const result = detectRiskFromWindows(
  currentWindow,  // 近30分钟数据
  yoyWindow,     // 上周同时段数据
  {
    targetConsecutiveMinutes: 5,    // 关注连续5分钟异常
    trendDeviationWindow: 10,       // 用10分钟窗口检测趋势偏离
    trendBiasThreshold: 0.8,        // 80%同向偏离才认为是趋势异常
    extendedPersistenceWeight: 1.5  // 扩展持续性权重
  }
);

// 智能告警
const alerts = intelligentAlerting(result);
alerts.forEach(alert => {
  console.log(`[${alert.level}] ${alert.type}: ${alert.message} (风险: ${alert.riskPercentage})`);
});

// 检查关键指标
console.log('风险概率:', (result.risk * 100).toFixed(1) + '%');
console.log('连续异常分钟数:', result.consCount);  
console.log('扩展持续性评分:', result.EP);
console.log('趋势偏离:', result.TD ? '是' : '否');
if (result.TD) {
  console.log('偏离方向:', result.trendBias.direction);
  console.log('偏离程度:', (result.trendBias.bias * 100).toFixed(1) + '%');
}

// 看板显示示例
console.log('=== 看板显示格式 ===');
console.log(`当前风险: ${(result.risk * 100).toFixed(1)}%`);
console.log(`状态: ${result.risk > 0.8 ? '高风险' : result.risk > 0.5 ? '中风险' : result.risk > 0.2 ? '低风险' : '正常'}`);
*/