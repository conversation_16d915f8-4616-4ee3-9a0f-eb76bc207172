/**
 * K-means聚类算法实现
 * 用于将坐标点根据距离分组
 */
class KMeansClustering {
    /**
     * 初始化K-means聚类
     * @param {number} k - 聚类数量
     * @param {number} maxIterations - 最大迭代次数
     */
    constructor(k, maxIterations = 100) {
        this.k = k;
        this.maxIterations = maxIterations;
    }

    /**
     * 计算两点之间的欧几里得距离
     * @param {Array} point1 - 第一个点 [lng, lat]
     * @param {Array} point2 - 第二个点 [lng, lat]
     * @returns {number} - 两点之间的距离
     */
    distance(point1, point2) {
        // 使用欧几里得距离计算
        const dx = point1[0] - point2[0];
        const dy = point1[1] - point2[1];
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 随机选择初始中心点
     * @param {Array} points - 所有点的数组
     * @returns {Array} - 初始中心点数组
     */
    initializeCentroids(points) {
        const centroids = [];
        const indices = new Set();
        
        // 确保有足够的点
        if (points.length < this.k) {
            throw new Error('点的数量少于聚类数量');
        }
        
        // 随机选择不重复的点作为初始中心
        while (centroids.length < this.k) {
            const index = Math.floor(Math.random() * points.length);
            if (!indices.has(index)) {
                indices.add(index);
                centroids.push([...points[index]]);
            }
        }
        
        return centroids;
    }

    /**
     * 将点分配到最近的中心点
     * @param {Array} points - 所有点的数组
     * @param {Array} centroids - 中心点数组
     * @returns {Array} - 每个点所属的聚类索引
     */
    assignPointsToClusters(points, centroids) {
        return points.map(point => {
            let minDistance = Infinity;
            let clusterIndex = 0;
            
            for (let i = 0; i < centroids.length; i++) {
                const distance = this.distance(point, centroids[i]);
                if (distance < minDistance) {
                    minDistance = distance;
                    clusterIndex = i;
                }
            }
            
            return clusterIndex;
        });
    }

    /**
     * 更新中心点位置
     * @param {Array} points - 所有点的数组
     * @param {Array} assignments - 每个点所属的聚类索引
     * @param {number} k - 聚类数量
     * @returns {Array} - 新的中心点数组
     */
    updateCentroids(points, assignments, k) {
        const newCentroids = Array(k).fill().map(() => [0, 0]);
        const counts = Array(k).fill(0);
        
        // 累加每个聚类中的点
        for (let i = 0; i < points.length; i++) {
            const clusterIndex = assignments[i];
            newCentroids[clusterIndex][0] += points[i][0];
            newCentroids[clusterIndex][1] += points[i][1];
            counts[clusterIndex]++;
        }
        
        // 计算平均值作为新的中心点
        for (let i = 0; i < k; i++) {
            if (counts[i] > 0) {
                newCentroids[i][0] /= counts[i];
                newCentroids[i][1] /= counts[i];
            }
        }
        
        return newCentroids;
    }

    /**
     * 执行K-means聚类
     * @param {Array} points - 所有点的数组 [[lng, lat], ...]
     * @returns {Object} - 聚类结果
     */
    cluster(points) {
        if (points.length === 0) {
            return { centroids: [], assignments: [] };
        }
        
        // 初始化中心点
        let centroids = this.initializeCentroids(points);
        let assignments = [];
        let iterations = 0;
        let converged = false;
        
        while (!converged && iterations < this.maxIterations) {
            // 分配点到最近的中心
            const newAssignments = this.assignPointsToClusters(points, centroids);
            
            // 检查是否收敛
            converged = true;
            if (assignments.length > 0) {
                for (let i = 0; i < assignments.length; i++) {
                    if (assignments[i] !== newAssignments[i]) {
                        converged = false;
                        break;
                    }
                }
            } else {
                converged = false;
            }
            
            assignments = newAssignments;
            
            // 如果未收敛，更新中心点
            if (!converged) {
                centroids = this.updateCentroids(points, assignments, this.k);
            }
            
            iterations++;
        }
        
        // 构建聚类结果
        const clusters = Array(this.k).fill().map(() => ({ 
            center: [0, 0],
            points: [],
            bounds: { min: [Infinity, Infinity], max: [-Infinity, -Infinity] }
        }));
        
        for (let i = 0; i < points.length; i++) {
            const clusterIndex = assignments[i];
            clusters[clusterIndex].points.push(points[i]);
            
            // 更新边界
            clusters[clusterIndex].bounds.min[0] = Math.min(clusters[clusterIndex].bounds.min[0], points[i][0]);
            clusters[clusterIndex].bounds.min[1] = Math.min(clusters[clusterIndex].bounds.min[1], points[i][1]);
            clusters[clusterIndex].bounds.max[0] = Math.max(clusters[clusterIndex].bounds.max[0], points[i][0]);
            clusters[clusterIndex].bounds.max[1] = Math.max(clusters[clusterIndex].bounds.max[1], points[i][1]);
        }
        
        // 设置中心点
        for (let i = 0; i < this.k; i++) {
            clusters[i].center = centroids[i];
        }
        
        return {
            clusters,
            assignments
        };
    }
}
