<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险检测算法演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.4/build/global/luxon.min.js"></script>
    <script src="risk-detecter.js"></script>
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #34495e;
            --accent: #3498db;
            --danger: #e74c3c;
            --warning: #f39c12;
            --success: #2ecc71;
            --light: #ecf0f1;
            --dark: #2c3e50;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        h1 {
            color: var(--primary);
            margin-bottom: 5px;
            font-size: 1.8rem;
        }

        .panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .panel:hover {
            transform: translateY(-5px);
        }

        .panel-title {
            font-size: 1.2rem;
            color: var(--primary);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title .badge {
            background: var(--accent);
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
        }

        @media (max-width: 1024px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }

        .input-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--secondary);
        }

        textarea, input[type="text"], input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-family: monospace;
            transition: border 0.3s;
        }

        textarea:focus, input[type="text"]:focus, input[type="number"]:focus {
            border-color: var(--accent);
            outline: none;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        button {
            background: var(--accent);
            color: white;
            border: none;
            padding: 12px 18px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        button i {
            margin-right: 5px;
        }

        button:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn-danger {
            background: var(--danger);
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: var(--warning);
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-success {
            background: var(--success);
        }

        .btn-success:hover {
            background: #27ae60;
        }

        .risk-meter {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
        }

        .risk-value {
            font-size: 2.8rem;
            font-weight: bold;
            margin: 10px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .risk-low { color: var(--success); }
        .risk-medium { color: var(--warning); }
        .risk-high { color: var(--danger); }

        .confidence-bar {
            height: 20px;
            background: #eee;
            border-radius: 10px;
            margin: 15px 0;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent) 0%, #2980b9 100%);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .alert-critical {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #c62828;
        }

        .alert-warning {
            background: #fff8e1;
            color: #f57c00;
            border-left: 4px solid #f57c00;
        }

        .alert-info {
            background: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #1565c0;
        }

        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
        }

        .details-table {
            width: 100%;
            border-collapse: collapse;
        }

        .details-table th, .details-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .details-table th {
            font-weight: 600;
            color: var(--primary);
            background: #f8f9fa;
        }

        .details-table tr:hover {
            background: #f8f9fa;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #eee;
        }

        .tab-button {
            padding: 10px 20px;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            position: relative;
            transition: all 0.3s;
        }

        .tab-button:hover {
            color: var(--accent);
        }

        .tab-button.active {
            color: var(--accent);
            font-weight: 500;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent);
            border-radius: 3px 3px 0 0;
        }

        .tab-content {
            padding: 20px 0;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .example-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .example-button {
            padding: 10px;
        }

        .config-toggle {
            margin-top: 10px;
            background: none;
            color: var(--accent);
            padding: 5px 10px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }

        .config-panel {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 6px;
            border-left: 4px solid var(--accent);
        }

        .config-panel.show {
            display: block;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        @media (max-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr;
            }

            .example-buttons {
                grid-template-columns: 1fr;
            }
        }

        .risk-band {
            height: 12px;
            background: #eee;
            border-radius: 6px;
            margin: 15px 0;
            position: relative;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .band-indicator {
            position: absolute;
            height: 100%;
            background: linear-gradient(90deg, var(--success) 0%, var(--warning) 50%, var(--danger) 100%);
            border-radius: 6px;
            transition: all 0.5s ease;
        }

        .band-marker {
            position: absolute;
            top: -5px;
            width: 3px;
            height: 22px;
            background: var(--dark);
            border-radius: 2px;
            transform: translateX(-50%);
            box-shadow: 0 0 3px rgba(0,0,0,0.2);
        }

        .band-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        .input-format {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
        }

        .format-btn {
            padding: 8px 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
        }

        .format-btn:hover {
            border-color: var(--accent);
        }

        .format-btn.active {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }

        .time-display {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .json-error {
            color: var(--danger);
            font-size: 0.9rem;
            margin-top: 5px;
            padding: 5px;
            background: #ffebee;
            border-radius: 4px;
        }

        .data-preview {
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 6px;
            font-size: 0.9rem;
            max-height: 120px;
            overflow-y: auto;
            border-left: 3px solid var(--accent);
        }

        .url-params-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid var(--accent);
            font-size: 0.9rem;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--accent);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .risk-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .risk-detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }

        .risk-detail-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
        }

        .risk-detail-label {
            font-size: 0.8rem;
            color: #666;
        }
    </style>
</head>
<body>
<div class="container">
    <header>
        <h1>风险检测算法演示</h1>
    </header>

    <div id="urlParamsInfo" class="url-params-info" style="display: none;">
        检测到URL参数，已自动填充数据并进行分析。
    </div>

    <div class="grid">
        <!-- 左侧：数据输入和配置 -->
        <div class="panel">
            <h2 class="panel-title">数据输入</h2>

            <div class="input-group">
                <label>数据格式</label>
                <div class="input-format">
                    <button id="formatSimple" class="format-btn active">简单数组</button>
                    <button id="formatJSON" class="format-btn">JSON数组</button>
                </div>
            </div>

            <div class="input-group" id="currentSimpleGroup">
                <label for="currentWindow">当前窗口数据（逗号分隔）</label>
                <textarea id="currentWindow" placeholder="例如: 100, 105, 98, 112, 120, 135, 128, 115, 108, 95"></textarea>
            </div>

            <div class="input-group" id="currentJSONGroup" style="display: none;">
                <label for="currentJSON">当前窗口数据（JSON格式）</label>
                <textarea id="currentJSON" placeholder='例如: [{"time": 1756568520, "value": "7919"}, {"time": 1756568580, "value": "7892"}]'></textarea>
                <div id="currentJSONError" class="json-error"></div>
                <div id="currentPreview" class="data-preview"></div>
            </div>

            <div class="input-group" id="yoySimpleGroup">
                <label for="yoyWindow">同比窗口数据（逗号分隔）</label>
                <textarea id="yoyWindow" placeholder="例如: 95, 100, 102, 98, 105, 110, 108, 103, 99, 96"></textarea>
            </div>

            <div class="input-group" id="yoyJSONGroup" style="display: none;">
                <label for="yoyJSON">同比窗口数据（JSON格式）</label>
                <textarea id="yoyJSON" placeholder='例如: [{"time": 1756568520, "value": "7919"}, {"time": 1756568580, "value": "7892"}]'></textarea>
                <div id="yoyJSONError" class="json-error"></div>
                <div id="yoyPreview" class="data-preview"></div>
            </div>

            <button id="analyzeBtn" class="btn-success">分析风险</button>

            <button id="configToggle" class="config-toggle">显示高级配置 ▼</button>

            <div id="configPanel" class="config-panel">
                <div class="config-grid">
                    <div class="input-group">
                        <label for="ewmaSpan">EWMA 跨度</label>
                        <input type="number" id="ewmaSpan" value="3" min="2" max="10">
                    </div>

                    <div class="input-group">
                        <label for="madWindow">MAD 窗口</label>
                        <input type="number" id="madWindow" value="7" min="5" max="15">
                    </div>

                    <div class="input-group">
                        <label for="zThreshold">Z 阈值</label>
                        <input type="number" id="zThreshold" value="2.5" step="0.1" min="1.5" max="5">
                    </div>

                    <div class="input-group">
                        <label for="ratioThreshold">比率阈值</label>
                        <input type="number" id="ratioThreshold" value="0.12" step="0.01" min="0.05" max="0.5">
                    </div>

                    <div class="input-group">
                        <label for="targetMinutes">目标连续分钟</label>
                        <input type="number" id="targetMinutes" value="5" min="1" max="10">
                    </div>

                    <div class="input-group">
                        <label for="adaptiveTuning">自适应调节</label>
                        <select id="adaptiveTuning">
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                </div>
            </div>

            <h3 class="panel-title">示例数据</h3>
            <div class="example-buttons">
                <button class="example-button" data-example="normal">正常流量</button>
                <button class="example-button" data-example="spike">突发高峰</button>
                <button class="example-button" data-example="small">小流量服务</button>
                <button class="example-button" data-example="persistent">持续异常</button>
            </div>
        </div>

        <!-- 中间：可视化图表 -->
        <div class="panel">
            <h2 class="panel-title">数据可视化</h2>
            <div class="chart-container">
                <canvas id="dataChart"></canvas>
            </div>

            <div class="loading" id="chartLoading">
                <div class="loading-spinner"></div>
                <p>正在分析数据...</p>
            </div>

            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="points">数据点详情</button>
                    <button class="tab-button" data-tab="adaptive">自适应信息</button>
                    <button class="tab-button" data-tab="trend">趋势分析</button>
                </div>

                <div class="tab-content">
                    <div id="tab-points" class="tab-pane active">
                        <div class="input-group">
                            <input type="text" id="searchPoint" placeholder="搜索时间点...">
                        </div>
                        <div id="pointsTable" style="max-height: 300px; overflow-y: auto;">
                            <p>请先进行分析以查看数据点详情</p>
                        </div>
                    </div>

                    <div id="tab-adaptive" class="tab-pane">
                        <div id="adaptiveInfo">
                            <p>请先进行分析以查看自适应信息</p>
                        </div>
                    </div>

                    <div id="tab-trend" class="tab-pane">
                        <div id="trendInfo">
                            <p>请先进行分析以查看趋势分析</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：风险结果和告警 -->
        <div class="panel">
            <h2 class="panel-title">风险评估</h2>

            <div class="risk-meter">
                <div>风险概率</div>
                <div id="riskValue" class="risk-value">-</div>
                <div id="riskLevel" class="risk-level">等待分析...</div>

                <div class="risk-band">
                    <div id="bandIndicator" class="band-indicator" style="left: 0%; width: 0%;"></div>
                    <div id="bandMarker" class="band-marker" style="left: 0%;"></div>
                </div>
                <div class="band-labels">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                </div>

                <div style="margin-top: 15px;">
                    <div>置信度</div>
                    <div class="confidence-bar">
                        <div id="confidenceFill" class="confidence-fill" style="width: 0%;"></div>
                    </div>
                    <div id="confidenceValue">未计算</div>
                </div>
            </div>

            <div class="risk-details">
                <div class="risk-detail-item">
                    <div class="risk-detail-value" id="severityValue">-</div>
                    <div class="risk-detail-label">严重性 (S)</div>
                </div>
                <div class="risk-detail-item">
                    <div class="risk-detail-value" id="magnitudeValue">-</div>
                    <div class="risk-detail-label">幅度 (M)</div>
                </div>
                <div class="risk-detail-item">
                    <div class="risk-detail-value" id="persistenceValue">-</div>
                    <div class="risk-detail-label">持续性 (P)</div>
                </div>
                <div class="risk-detail-item">
                    <div class="risk-detail-value" id="extendedPersistenceValue">-</div>
                    <div class="risk-detail-label">扩展持续性 (EP)</div>
                </div>
            </div>

            <h3 class="panel-title" style="margin-top: 20px;">智能告警</h3>
            <div id="alertsContainer">
                <p>等待分析结果...</p>
            </div>
        </div>
    </div>
</div>

<script>
    // 全局变量
    let dataChart = null;
    let currentResult = null;
    let currentFormat = 'simple'; // 'simple' 或 'json'

    // 示例数据 - 简单格式
    const examplesSimple = {
        normal: {
            current: [100, 105, 98, 112, 120, 135, 128, 115, 108, 95],
            yoy: [95, 100, 102, 98, 105, 110, 108, 103, 99, 96]
        },
        spike: {
            current: [100, 105, 98, 112, 320, 335, 128, 115, 108, 95],
            yoy: [95, 100, 102, 98, 105, 110, 108, 103, 99, 96]
        },
        small: {
            current: [2, 3, 1, 5, 8, 12, 3, 0, 2, 1],
            yoy: [1, 2, 2, 3, 2, 4, 2, 1, 1, 2]
        },
        persistent: {
            current: [100, 120, 135, 150, 165, 180, 195, 210, 225, 240],
            yoy: [95, 100, 102, 98, 105, 110, 108, 103, 99, 96]
        }
    };

    // 示例数据 - JSON格式
    const examplesJSON = {
        normal: {
            current: [
                {time: 1756568520, value: "100"},
                {time: 1756568580, value: "105"},
                {time: 1756568640, value: "98"},
                {time: 1756568700, value: "112"},
                {time: 1756568760, value: "120"},
                {time: 1756568820, value: "135"},
                {time: 1756568880, value: "128"},
                {time: 1756568940, value: "115"},
                {time: 1756569000, value: "108"},
                {time: 1756569060, value: "95"}
            ],
            yoy: [
                {time: 1756568520, value: "95"},
                {time: 1756568580, value: "100"},
                {time: 1756568640, value: "102"},
                {time: 1756568700, value: "98"},
                {time: 1756568760, value: "105"},
                {time: 1756568820, value: "110"},
                {time: 1756568880, value: "108"},
                {time: 1756568940, value: "103"},
                {time: 1756569000, value: "99"},
                {time: 1756569060, value: "96"}
            ]
        },
        spike: {
            current: [
                {time: 1756568520, value: "100"},
                {time: 1756568580, value: "105"},
                {time: 1756568640, value: "98"},
                {time: 1756568700, value: "112"},
                {time: 1756568760, value: "320"},
                {time: 1756568820, value: "335"},
                {time: 1756568880, value: "128"},
                {time: 1756568940, value: "115"},
                {time: 1756569000, value: "108"},
                {time: 1756569060, value: "95"}
            ],
            yoy: [
                {time: 1756568520, value: "95"},
                {time: 1756568580, value: "100"},
                {time: 1756568640, value: "102"},
                {time: 1756568700, value: "98"},
                {time: 1756568760, value: "105"},
                {time: 1756568820, value: "110"},
                {time: 1756568880, value: "108"},
                {time: 1756568940, value: "103"},
                {time: 1756569000, value: "99"},
                {time: 1756569060, value: "96"}
            ]
        }
    };

    // 解析URL参数
    function getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        const currentParam = params.get('current');
        const yoyParam = params.get('yoy');

        if (currentParam && yoyParam) {
            return {
                current: currentParam.split(',').map(x => parseFloat(x.trim())),
                yoy: yoyParam.split(',').map(x => parseFloat(x.trim()))
            };
        }

        return null;
    }

    // DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图表
        initChart();

        // 检查URL参数
        const urlParams = getUrlParams();
        if (urlParams) {
            // 使用URL参数填充数据
            document.getElementById('currentWindow').value = urlParams.current.join(', ');
            document.getElementById('yoyWindow').value = urlParams.yoy.join(', ');

            // 显示URL参数信息
            document.getElementById('urlParamsInfo').style.display = 'block';

            // 自动分析
            setTimeout(() => {
                analyzeData();
            }, 500);
        }

        // 绑定分析按钮事件
        document.getElementById('analyzeBtn').addEventListener('click', analyzeData);

        // 绑定配置面板切换
        document.getElementById('configToggle').addEventListener('click', function() {
            const panel = document.getElementById('configPanel');
            const isShowing = panel.classList.contains('show');

            if (isShowing) {
                panel.classList.remove('show');
                this.innerHTML = '显示高级配置 ▼';
            } else {
                panel.classList.add('show');
                this.innerHTML = '隐藏高级配置 ▲';
            }
        });

        // 绑定格式切换
        document.getElementById('formatSimple').addEventListener('click', function() {
            setInputFormat('simple');
        });

        document.getElementById('formatJSON').addEventListener('click', function() {
            setInputFormat('json');
        });

        // 绑定JSON输入验证
        document.getElementById('currentJSON').addEventListener('input', function() {
            validateJSON(this.value, 'current');
        });

        document.getElementById('yoyJSON').addEventListener('input', function() {
            validateJSON(this.value, 'yoy');
        });

        // 绑定示例按钮事件
        document.querySelectorAll('.example-button').forEach(button => {
            button.addEventListener('click', function() {
                const exampleType = this.getAttribute('data-example');
                loadExample(exampleType);
            });
        });

        // 绑定标签切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有激活状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('active');
                });

                // 添加当前激活状态
                this.classList.add('active');
                const tabId = 'tab-' + this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 绑定搜索功能
        document.getElementById('searchPoint').addEventListener('input', function() {
            filterPointsTable(this.value);
        });
    });

    // 设置输入格式
    function setInputFormat(format) {
        currentFormat = format;

        // 更新按钮状态
        document.getElementById('formatSimple').classList.toggle('active', format === 'simple');
        document.getElementById('formatJSON').classList.toggle('active', format === 'json');

        // 显示/隐藏对应的输入区域
        document.getElementById('currentSimpleGroup').style.display = format === 'simple' ? 'block' : 'none';
        document.getElementById('yoySimpleGroup').style.display = format === 'simple' ? 'block' : 'none';
        document.getElementById('currentJSONGroup').style.display = format === 'json' ? 'block' : 'none';
        document.getElementById('yoyJSONGroup').style.display = format === 'json' ? 'block' : 'none';
    }

    // 验证JSON格式
    function validateJSON(jsonString, type) {
        const errorElement = document.getElementById(`${type}JSONError`);
        const previewElement = document.getElementById(`${type}Preview`);

        if (!jsonString.trim()) {
            errorElement.textContent = '';
            previewElement.innerHTML = '';
            return false;
        }

        try {
            const data = JSON.parse(jsonString);

            if (!Array.isArray(data)) {
                errorElement.textContent = '错误：必须是一个数组';
                previewElement.innerHTML = '';
                return false;
            }

            // 检查数组元素结构
            if (data.length > 0) {
                const firstItem = data[0];
                if (typeof firstItem !== 'object' || firstItem === null ||
                    !('value' in firstItem) || !('time' in firstItem)) {
                    errorElement.textContent = '错误：数组元素必须包含time和value属性';
                    previewElement.innerHTML = '';
                    return false;
                }
            }

            errorElement.textContent = '';

            // 显示预览
            let previewHTML = `<div>数据点数: ${data.length}</div>`;
            if (data.length > 0) {
                previewHTML += `<div>时间范围: ${formatTime(data[0].time)} - ${formatTime(data[data.length-1].time)}</div>`;
                previewHTML += `<div>值范围: ${Math.min(...data.map(d => Number(d.value)))} - ${Math.max(...data.map(d => Number(d.value)))}</div>`;
            }

            previewElement.innerHTML = previewHTML;
            return true;
        } catch (e) {
            errorElement.textContent = 'JSON格式错误: ' + e.message;
            previewElement.innerHTML = '';
            return false;
        }
    }

    // 格式化时间戳
    function formatTime(timestamp) {
        if (!timestamp) return '未知';

        // 假设时间戳是秒级
        const date = luxon.DateTime.fromSeconds(Number(timestamp));
        return date.toFormat('yyyy-MM-dd HH:mm:ss');
    }

    // 初始化图表
    function initChart() {
        const ctx = document.getElementById('dataChart').getContext('2d');
        dataChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '当前窗口',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: '同比窗口',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: '基线',
                        data: [],
                        borderColor: 'rgb(255, 159, 64)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        tension: 0.3,
                        fill: false,
                        pointStyle: false
                    },
                    {
                        label: '异常点',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.8)',
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        showLine: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '数值'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间点'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(2);
                                }
                                return label;
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    }

    // 加载示例数据
    function loadExample(type) {
        if (currentFormat === 'simple') {
            const example = examplesSimple[type];
            if (!example) return;

            document.getElementById('currentWindow').value = example.current.join(', ');
            document.getElementById('yoyWindow').value = example.yoy.join(', ');
        } else {
            const example = examplesJSON[type];
            if (!example) return;

            document.getElementById('currentJSON').value = JSON.stringify(example.current, null, 2);
            document.getElementById('yoyJSON').value = JSON.stringify(example.yoy, null, 2);

            // 验证JSON
            validateJSON(document.getElementById('currentJSON').value, 'current');
            validateJSON(document.getElementById('yoyJSON').value, 'yoy');
        }

        // 自动分析
        analyzeData();
    }

    // 分析数据
    function analyzeData() {
        let currentData, yoyData;

        // 显示加载状态
        document.getElementById('chartLoading').style.display = 'block';

        try {
            if (currentFormat === 'simple') {
                // 获取简单格式数据
                const currentInput = document.getElementById('currentWindow').value;
                const yoyInput = document.getElementById('yoyWindow').value;

                if (!currentInput || !yoyInput) {
                    alert('请输入当前窗口和同比窗口数据');
                    document.getElementById('chartLoading').style.display = 'none';
                    return;
                }

                // 解析简单格式数据
                currentData = currentInput.split(',').map(x => parseFloat(x.trim()));
                yoyData = yoyInput.split(',').map(x => parseFloat(x.trim()));
            } else {
                // 获取JSON格式数据
                const currentInput = document.getElementById('currentJSON').value;
                const yoyInput = document.getElementById('yoyJSON').value;

                if (!currentInput || !yoyInput) {
                    alert('请输入当前窗口和同比窗口数据');
                    document.getElementById('chartLoading').style.display = 'none';
                    return;
                }

                // 验证并解析JSON数据
                if (!validateJSON(currentInput, 'current') || !validateJSON(yoyInput, 'yoy')) {
                    alert('JSON数据格式错误，请检查输入');
                    document.getElementById('chartLoading').style.display = 'none';
                    return;
                }

                // 直接使用JSON对象（算法支持{time, value}格式）
                currentData = JSON.parse(currentInput);
                yoyData = JSON.parse(yoyInput);
            }

            if (currentData.length !== yoyData.length) {
                alert('当前窗口和同比窗口数据长度必须相同');
                document.getElementById('chartLoading').style.display = 'none';
                return;
            }
        } catch (e) {
            alert('数据解析错误: ' + e.message);
            document.getElementById('chartLoading').style.display = 'none';
            return;
        }

        // 获取配置
        const config = {
            ewmaSpan: parseInt(document.getElementById('ewmaSpan').value),
            madWindow: parseInt(document.getElementById('madWindow').value),
            z_th: parseFloat(document.getElementById('zThreshold').value),
            ratio_th: parseFloat(document.getElementById('ratioThreshold').value),
            targetConsecutiveMinutes: parseInt(document.getElementById('targetMinutes').value),
            enableAdaptiveTuning: document.getElementById('adaptiveTuning').value === 'true'
        };

        // 调用风险检测算法
        try {
            setTimeout(() => {
                currentResult = detectRiskFromWindows(currentData, yoyData, config);
                updateUI(currentResult);
                document.getElementById('chartLoading').style.display = 'none';
            }, 100);
        } catch (e) {
            alert('分析过程中出错: ' + e.message);
            console.error(e);
            document.getElementById('chartLoading').style.display = 'none';
        }
    }

    // 更新UI显示结果
    function updateUI(result) {
        // 更新风险值
        const riskPercent = (result.risk * 100).toFixed(1);
        document.getElementById('riskValue').textContent = riskPercent + '%';

        // 设置风险级别样式
        const riskLevel = document.getElementById('riskLevel');
        if (result.risk > 0.8) {
            riskLevel.textContent = '高风险';
            riskLevel.className = 'risk-level risk-high';
            document.getElementById('riskValue').className = 'risk-value risk-high';
        } else if (result.risk > 0.5) {
            riskLevel.textContent = '中风险';
            riskLevel.className = 'risk-level risk-medium';
            document.getElementById('riskValue').className = 'risk-value risk-medium';
        } else if (result.risk > 0.2) {
            riskLevel.textContent = '低风险';
            riskLevel.className = 'risk-level risk-low';
            document.getElementById('riskValue').className = 'risk-value risk-low';
        } else {
            riskLevel.textContent = '正常';
            riskLevel.className = 'risk-level risk-low';
            document.getElementById('riskValue').className = 'risk-value risk-low';
        }

        // 更新风险区间指示器
        const bandIndicator = document.getElementById('bandIndicator');
        const bandMarker = document.getElementById('bandMarker');
        const riskPosition = result.risk * 100;

        bandIndicator.style.left = Math.max(0, riskPosition - 5) + '%';
        bandIndicator.style.width = '10%';
        bandMarker.style.left = riskPosition + '%';

        // 更新置信度
        const confidencePercent = (result.confidence * 100).toFixed(0);
        document.getElementById('confidenceFill').style.width = confidencePercent + '%';
        document.getElementById('confidenceValue').textContent = confidencePercent + '%';

        // 更新关键指标
        document.getElementById('severityValue').textContent = result.S;
        document.getElementById('magnitudeValue').textContent = result.M;
        document.getElementById('persistenceValue').textContent = result.P;
        document.getElementById('extendedPersistenceValue').textContent = result.EP;
        document.getElementById('trendDeviationValue').textContent = result.TD ? '是' : '否';
        document.getElementById('consecutiveValue').textContent = result.consCount + ' 分钟';

        // 更新图表
        updateChart(result);

        // 更新数据点表格
        updatePointsTable(result);

        // 更新自适应信息
        updateAdaptiveInfo(result);

        // 更新趋势分析
        updateTrendInfo(result);

        // 更新告警信息
        updateAlerts(result);
    }

    // 更新图表
    function updateChart(result) {
        // 确定标签 - 如果有时间信息，使用时间，否则使用索引
        let labels;
        if (result.perPoint.length > 0 && result.perPoint[0].time) {
            labels = result.perPoint.map(point => formatTime(point.time));
        } else {
            labels = result.perPoint.map((point, index) => `T${index}`);
        }

        const currentValues = result.perPoint.map(point => point.C);
        const yoyValues = result.perPoint.map(point => point.Y);
        const baselineValues = result.perPoint.map(point => point.B);

        // 异常点数据
        const anomalyPoints = [];
        result.perPoint.forEach((point, index) => {
            if (point.isAnomaly) {
                anomalyPoints.push({x: labels[index], y: point.C});
            }
        });

        dataChart.data.labels = labels;
        dataChart.data.datasets[0].data = currentValues;
        dataChart.data.datasets[1].data = yoyValues;
        dataChart.data.datasets[2].data = baselineValues;
        dataChart.data.datasets[3].data = anomalyPoints;

        dataChart.update();
    }

    // 更新数据点表格
    function updatePointsTable(result) {
        const tableContainer = document.getElementById('pointsTable');
        let html = '<table class="details-table"><tr><th>时间点</th><th>当前值</th><th>同比值</th><th>基线</th><th>残差</th><th>Z分数</th><th>异常</th></tr>';

        result.perPoint.forEach((point, index) => {
            const timeLabel = point.time ? formatTime(point.time) : `T${index}`;

            html += `<tr>
                    <td>${timeLabel}</td>
                    <td>${point.C.toFixed(2)}</td>
                    <td>${point.Y.toFixed(2)}</td>
                    <td>${point.B.toFixed(2)}</td>
                    <td>${point.r.toFixed(2)}</td>
                    <td>${point.z.toFixed(2)}</td>
                    <td>${point.isAnomaly ? '是' : '否'}</td>
                </tr>`;
        });

        html += '</table>';
        tableContainer.innerHTML = html;
    }

    // 过滤数据点表格
    function filterPointsTable(query) {
        const table = document.querySelector('#pointsTable table');
        if (!table) return;

        const rows = table.getElementsByTagName('tr');
        for (let i = 1; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length; j++) {
                const cellText = cells[j].textContent || cells[j].innerText;
                if (cellText.toLowerCase().includes(query.toLowerCase())) {
                    found = true;
                    break;
                }
            }

            rows[i].style.display = found ? '' : 'none';
        }
    }

    // 更新自适应信息
    function updateAdaptiveInfo(result) {
        const adaptiveInfo = document.getElementById('adaptiveInfo');

        if (!result.adaptiveInfo) {
            adaptiveInfo.innerHTML = '<p>未启用自适应调节或无可用的调节信息</p>';
            return;
        }

        const stats = result.adaptiveInfo.yoyStats;
        const adj = result.adaptiveInfo.adjustments;

        let html = '<table class="details-table">';
        html += '<tr><th colspan="2">同比数据特征</th></tr>';
        html += `<tr><td>流量中位数</td><td>${stats.median.toFixed(2)}</td></tr>`;
        html += `<tr><td>变异系数 (CV)</td><td>${stats.cv.toFixed(2)}</td></tr>`;
        html += `<tr><td>相对MAD</td><td>${stats.relativeMAD.toFixed(2)}</td></tr>`;
        html += `<tr><td>爆发性</td><td>${stats.burstiness.toFixed(2)}</td></tr>`;
        html += `<tr><td>零值比例</td><td>${(stats.zeroRatio * 100).toFixed(1)}%</td></tr>`;

        html += '<tr><th colspan="2">自适应调节系数</th></tr>';
        html += `<tr><td>规模调节</td><td>${adj.scale.toFixed(2)}</td></tr>`;
        html += `<tr><td>变异性调节</td><td>${adj.variability.toFixed(2)}</td></tr>`;
        html += `<tr><td>稳定性调节</td><td>${adj.stability.toFixed(2)}</td></tr>`;
        html += `<tr><td>爆发性调节</td><td>${adj.burst.toFixed(2)}</td></tr>`;

        html += '<tr><th colspan="2">调节后参数</th></tr>';
        html += `<tr><td>Z阈值</td><td>${result.details.config.z_th.toFixed(2)}</td></tr>`;
        html += `<tr><td>比率阈值</td><td>${result.details.config.ratio_th.toFixed(3)}</td></tr>`;
        html += `<tr><td>EWMA跨度</td><td>${result.details.config.ewmaSpan}</td></tr>`;
        html += `<tr><td>MAD窗口</td><td>${result.details.config.madWindow}</td></tr>`;
        html += `<tr><td>最小有意义变化</td><td>${result.details.config.minMeaningfulChange.toFixed(2)}</td></tr>`;

        html += '</table>';
        adaptiveInfo.innerHTML = html;
    }

    // 更新趋势分析信息
    function updateTrendInfo(result) {
        const trendInfo = document.getElementById('trendInfo');
        const trend = result.trendBias;

        let html = '<table class="details-table">';
        html += '<tr><th colspan="2">趋势偏离分析</th></tr>';
        html += `<tr><td>检测到偏离</td><td>${trend.isDeviation ? '是' : '否'}</td></tr>`;

        if (trend.isDeviation) {
            html += `<tr><td>偏离方向</td><td>${trend.direction}</td></tr>`;
            html += `<tr><td>偏离程度</td><td>${(trend.bias * 100).toFixed(1)}%</td></tr>`;
            html += `<tr><td>正向偏离比例</td><td>${(trend.positiveBias * 100).toFixed(1)}%</td></tr>`;
            html += `<tr><td>负向偏离比例</td><td>${(trend.negativeBias * 100).toFixed(1)}%</td></tr>`;
            html += `<tr><td>平均绝对残差</td><td>${trend.avgAbsResidual.toFixed(2)}</td></tr>`;

            if (trend.direction === 'UP') {
                html += `<tr><td>平均正向残差</td><td>${trend.avgPositiveResidual.toFixed(2)}</td></tr>`;
            } else if (trend.direction === 'DOWN') {
                html += `<tr><td>平均负向残差</td><td>${trend.avgNegativeResidual.toFixed(2)}</td></tr>`;
            }
        }

        html += `<tr><td>分析窗口大小</td><td>${trend.windowSize}</td></tr>`;
        html += `<tr><td>正向点数</td><td>${trend.positiveCount}</td></tr>`;
        html += `<tr><td>负向点数</td><td>${trend.negativeCount}</td></tr>`;
        html += `<tr><td>零点数</td><td>${trend.zeroCount}</td></tr>`;

        html += '</table>';
        trendInfo.innerHTML = html;
    }

    // 更新告警信息
    function updateAlerts(result) {
        const alerts = intelligentAlerting(result);
        const alertsContainer = document.getElementById('alertsContainer');

        if (alerts.length === 0) {
            alertsContainer.innerHTML = '<div class="alert alert-info">未检测到显著异常</div>';
            return;
        }

        let html = '';
        alerts.forEach(alert => {
            let alertClass = 'alert-info';
            if (alert.level === 'CRITICAL') alertClass = 'alert-critical';
            else if (alert.level === 'WARNING') alertClass = 'alert-warning';

            html += `<div class="alert ${alertClass}">
                    <strong>${alert.level}: ${alert.type}</strong><br>
                    ${alert.message}<br>
                    <small>风险: ${alert.riskPercentage}, 置信度: ${(alert.confidence * 100).toFixed(0)}%</small>
                    ${alert.note ? `<br><small>${alert.note}</small>` : ''}
                </div>`;
        });

        alertsContainer.innerHTML = html;
    }
</script>
</body>
</html>