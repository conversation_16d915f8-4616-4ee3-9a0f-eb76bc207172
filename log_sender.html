<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>CSV 批量 GET 请求工具</title>
  <style>
    :root { --fg:#111; --muted:#666; --bg:#fff; --accent:#2563eb; --ok:#16a34a; --warn:#b45309; --err:#dc2626; }
    * { box-sizing: border-box; }
    body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "PingFang SC", "Noto Sans CJK SC", "Microsoft YaHei", sans-serif; margin: 0; color: var(--fg); background: #f7f7f8; }
    header { background: var(--bg); position: sticky; top:0; z-index: 10; box-shadow: 0 1px 0 rgba(0,0,0,.06); }
    .wrap { max-width: 1100px; margin: 0 auto; padding: 16px; }
    h1 { font-size: 20px; margin: 0; }
    .card { background: var(--bg); border-radius: 16px; padding: 16px; box-shadow: 0 1px 3px rgba(0,0,0,.06); margin: 16px 0; }
    .row { display: flex; gap: 12px; flex-wrap: wrap; align-items: center; }
    label { font-size: 14px; color: var(--muted); }
    input[type="file"], input[type="text"], button { font-size: 14px; }
    input[type="text"], input[type="number"] { padding: 8px 10px; border: 1px solid #ddd; border-radius: 10px; min-width: 240px; background: #fff; }
    button { padding: 10px 14px; border-radius: 999px; border: none; background: var(--accent); color: #fff; cursor: pointer; }
    button.secondary { background: #e5e7eb; color: #111; }
    button.ghost { background: transparent; color: var(--accent); border: 1px solid var(--accent);} 
    button:disabled { opacity: .5; cursor: not-allowed; }
    .stats { display: grid; grid-template-columns: repeat(5, minmax(140px,1fr)); gap: 12px; }
    .stat { background: #fafafa; border: 1px solid #eee; border-radius: 12px; padding: 12px; }
    .stat .label { color: var(--muted); font-size: 12px; }
    .stat .val { font-weight: 700; font-size: 20px; margin-top: 6px; }
    .progress { height: 10px; background: #eee; border-radius: 999px; overflow: hidden; }
    .bar { height: 100%; width: 0%; background: var(--accent); transition: width .2s ease; }
    table { width: 100%; border-collapse: collapse; font-size: 13px; }
    th, td { border-bottom: 1px solid #eee; padding: 8px; text-align: left; }
    th { background: #fafafa; position: sticky; top: 0; }
    .log { height: 180px; overflow: auto; background: #0b1220; color: #cbd5e1; padding: 12px; border-radius: 12px; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 12px; }
    .pill { display: inline-flex; align-items: center; gap: 6px; border-radius: 999px; padding: 4px 10px; font-size: 12px; }
    .ok { background: #ecfdf5; color: var(--ok); border: 1px solid #86efac; }
    .err { background: #fef2f2; color: var(--err); border: 1px solid #fecaca; }
    .warn { background: #fff7ed; color: var(--warn); border: 1px solid #fed7aa; }
    footer { color: var(--muted); font-size: 12px; }
    .hint { color: var(--muted); font-size: 12px; }
    .hidden { display: none !important; }
  </style>
</head>
<body>
  <header>
    <div class="wrap">
      <h1>CSV 批量 GET 请求工具</h1>
      <div class="hint">按 10 条/批次，向 <code>https://mk-group.cn-hangzhou.log.aliyuncs.com/logstores/emenu-mini/track</code> 发送 GET 请求，并携带请求头 <code>x-log-apiversion: 0.6.0</code>。</div>
    </div>
  </header>

  <main class="wrap">
    <section class="card">
      <div class="row" style="justify-content: space-between; align-items:flex-end;">
        <div>
          <label for="csvFile">选择 CSV 文件</label><br />
          <input type="file" id="csvFile" accept=".csv" />
        </div>
        <div class="row">
          <button id="downloadTemplate" class="secondary" type="button">下载模板</button>
          <button id="parseBtn" type="button">解析预览</button>
        </div>
      </div>
      <div id="requiredCols" class="hint" style="margin-top:8px;">必需列：storeSn, merchantSn, description, duration, totalTime</div>
    </section>

    <section class="card">
      <div class="row" style="justify-content: space-between;">
        <div class="stats">
          <div class="stat"><div class="label">总记录</div><div class="val" id="statTotal">0</div></div>
          <div class="stat"><div class="label">总批次（10/批）</div><div class="val" id="statBatches">0</div></div>
          <div class="stat"><div class="label">已成功</div><div class="val" id="statOk">0</div></div>
          <div class="stat"><div class="label">已失败</div><div class="val" id="statFail">0</div></div>
          <div class="stat"><div class="label">进度</div><div class="val" id="statProg">0%</div></div>
        </div>
        <div class="row">
          <button id="startBtn" type="button" disabled>开始发送</button>
          <button id="cancelBtn" class="ghost" type="button" disabled>取消</button>
          <button id="exportBtn" class="secondary" type="button" disabled>导出结果</button>
        </div>
      </div>
      <div class="progress" style="margin-top:12px;">
        <div class="bar" id="bar"></div>
      </div>
      <div class="hint" style="margin-top:8px;">提示：若浏览器跨域（CORS）受限，请在允许的受信源环境下使用，或经由后端/网关代理。前端直连无法绕过服务端 CORS 限制。</div>
    </section>

    <section class="card" id="previewCard" style="display:none;">
      <div class="row" style="justify-content: space-between;">
        <h3 style="margin:0;">预览（前 20 条）</h3>
        <span id="previewCount" class="pill warn">0 行</span>
      </div>
      <div style="max-height: 280px; overflow: auto; border: 1px solid #eee; border-radius: 12px; margin-top:8px;">
        <table id="previewTable"></table>
      </div>
    </section>

    <section class="card">
      <div class="row" style="justify-content: space-between; align-items:center;">
        <h3 style="margin:0;">执行日志</h3>
        <div>
          <span class="pill ok" id="hdrOk">OK: 0</span>
          <span class="pill err" id="hdrErr" style="margin-left:6px;">ERR: 0</span>
        </div>
      </div>
      <pre class="log" id="log"></pre>
    </section>

    <footer class="wrap" style="margin-bottom: 24px;">
      <div>Made with ❤️ — 单页离线工具，数据仅在本地浏览器内处理。</div>
    </footer>
  </main>

<script>
(function(){
  const ENDPOINT = 'https://mk-group.cn-hangzhou.log.aliyuncs.com/logstores/emenu-mini/track';
  const REQUIRED = ['storeSn','merchantSn','description','duration','totalTime'];
  const BATCH_SIZE = 10;

  const els = {
    file: document.getElementById('csvFile'),
    parseBtn: document.getElementById('parseBtn'),
    startBtn: document.getElementById('startBtn'),
    cancelBtn: document.getElementById('cancelBtn'),
    exportBtn: document.getElementById('exportBtn'),
    downloadTemplate: document.getElementById('downloadTemplate'),
    statTotal: document.getElementById('statTotal'),
    statBatches: document.getElementById('statBatches'),
    statOk: document.getElementById('statOk'),
    statFail: document.getElementById('statFail'),
    statProg: document.getElementById('statProg'),
    bar: document.getElementById('bar'),
    log: document.getElementById('log'),
    hdrOk: document.getElementById('hdrOk'),
    hdrErr: document.getElementById('hdrErr'),
    previewCard: document.getElementById('previewCard'),
    previewCount: document.getElementById('previewCount'),
    previewTable: document.getElementById('previewTable'),
  };

  let rows = [];
  let controller = null; // AbortController for cancel
  let results = []; // {index, status: 'ok'|'err', httpStatus, error}

  function log(msg){
    const ts = new Date().toISOString().replace('T',' ').replace('Z','');
    els.log.textContent += `[${ts}] ${msg}\n`;
    els.log.scrollTop = els.log.scrollHeight;
  }

  function toCSV(rows){
    if (!rows.length) return '';
    const headers = Object.keys(rows[0]);
    const esc = v => {
      if (v == null) return '';
      const s = String(v);
      if (/[",\n]/.test(s)) return '"' + s.replace(/"/g,'""') + '"';
      return s;
    };
    const lines = [headers.join(',')];
    for (const r of rows){
      lines.push(headers.map(h=>esc(r[h])).join(','));
    }
    return lines.join('\n');
  }

  function download(filename, content, mime='text/plain'){
    const blob = new Blob([content], {type: mime});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url; a.download = filename; a.click();
    setTimeout(()=>URL.revokeObjectURL(url), 1000);
  }

  function parseCSV(text){
    // Robust-ish CSV parser supporting quotes per RFC 4180
    const rows = [];
    let i = 0, field = '', record = [], inQuotes = false;
    while (i < text.length){
      const c = text[i];
      if (inQuotes){
        if (c === '"'){
          if (text[i+1] === '"'){ field += '"'; i += 2; continue; }
          inQuotes = false; i++; continue;
        } else { field += c; i++; continue; }
      } else {
        if (c === '"'){ inQuotes = true; i++; continue; }
        if (c === ','){ record.push(field); field=''; i++; continue; }
        if (c === '\n'){
          record.push(field); field=''; rows.push(record); record=[]; i++; continue; }
        if (c === '\r'){ // handle CRLF
          if (text[i+1] === '\n'){ i+=2; record.push(field); field=''; rows.push(record); record=[]; continue; }
          // lone CR
          record.push(field); field=''; rows.push(record); record=[]; i++; continue;
        }
        field += c; i++;
      }
    }
    // last field
    if (field !== '' || record.length){ record.push(field); rows.push(record); }
    // remove possible trailing empty line
    if (rows.length && rows[rows.length-1].length === 1 && rows[rows.length-1][0] === '') rows.pop();
    if (!rows.length) return [];
    const headers = rows[0].map(h=>h.trim());
    const list = [];
    for (let r = 1; r < rows.length; r++){
      const obj = {};
      for (let c = 0; c < headers.length; c++){
        obj[headers[c]] = rows[r][c] !== undefined ? rows[r][c] : '';
      }
      list.push(obj);
    }
    return list;
  }

  function validateHeaders(list){
    if (!list.length) return {ok:false, miss: REQUIRED.slice()};
    const headers = Object.keys(list[0]);
    const miss = REQUIRED.filter(k => !headers.includes(k));
    return {ok: miss.length === 0, miss};
  }

  function renderPreview(list){
    const head = `<tr>${REQUIRED.map(h=>`<th>${h}</th>`).join('')}</tr>`;
    const rowsHtml = list.slice(0,20).map(r=>`<tr>${REQUIRED.map(h=>`<td>${escapeHtml(r[h])}</td>`).join('')}</tr>`).join('');
    els.previewTable.innerHTML = head + rowsHtml;
    els.previewCard.style.display = '';
    els.previewCount.textContent = `${list.length} 行`;
  }

  function escapeHtml(s){
    if (s == null) return '';
    return String(s)
      .replace(/&/g,'&amp;')
      .replace(/</g,'&lt;')
      .replace(/>/g,'&gt;')
      .replace(/"/g,'&quot;')
      .replace(/'/g,'&#39;');
  }

  function updateStats(){
    const total = rows.length;
    const ok = results.filter(r=>r.status==='ok').length;
    const err = results.filter(r=>r.status==='err').length;
    const done = ok + err;
    const prog = total ? Math.round(done/total*100) : 0;
    els.statTotal.textContent = total;
    els.statBatches.textContent = Math.ceil(total / BATCH_SIZE);
    els.statOk.textContent = ok;
    els.statFail.textContent = err;
    els.statProg.textContent = prog + '%';
    els.bar.style.width = prog + '%';
    els.hdrOk.textContent = `OK: ${ok}`;
    els.hdrErr.textContent = `ERR: ${err}`;
  }

  function buildUrl(row){
    const params = new URLSearchParams();
    for (const key of REQUIRED){
      let val = row[key];
      if (key === 'duration' || key === 'totalTime'){
        // 尝试转换为数字，保持原值在非数字情况下也传递字符串
        const n = Number(val);
        val = Number.isFinite(n) ? String(n) : String(val ?? '');
      }
      params.set(key, val ?? '');
    }
    return `${ENDPOINT}?${params.toString()}`;
  }

  async function sendOne(index, row){
    const url = buildUrl(row);
    try {
      const res = await fetch(url, {
        method: 'GET',
        headers: { 'x-log-apiversion': '0.6.0' },
        signal: controller.signal,
        // mode: 'cors' // 默认即为 cors；如服务端未允许，将触发 CORS 错误
      });
      if (!res.ok){
        results[index] = { index, status:'err', httpStatus: res.status, error: `HTTP ${res.status}` };
        log(`❌ [#${index+1}] ${url} -> HTTP ${res.status}`);
      } else {
        results[index] = { index, status:'ok', httpStatus: res.status };
        log(`✅ [#${index+1}] ${url} -> HTTP ${res.status}`);
      }
    } catch (e){
      const errMsg = (e && e.name === 'AbortError') ? '已取消' : (e && e.message) ? e.message : String(e);
      results[index] = { index, status:'err', httpStatus: 0, error: errMsg };
      log(`❌ [#${index+1}] ${url} -> ${errMsg}`);
    }
    updateStats();
  }

  async function runBatches(){
    results = Array(rows.length).fill(null);
    updateStats();
    els.startBtn.disabled = true;
    els.cancelBtn.disabled = false;
    els.exportBtn.disabled = true;
    controller = new AbortController();

    const totalBatches = Math.ceil(rows.length / BATCH_SIZE);
    for (let b = 0; b < totalBatches; b++){
      if (controller.signal.aborted) break;
      const start = b * BATCH_SIZE;
      const end = Math.min(start + BATCH_SIZE, rows.length);
      const batch = rows.slice(start, end);
      log(`🚀 开始批次 ${b+1}/${totalBatches}，记录 ${start+1} - ${end}`);
      await Promise.all(batch.map((row, i) => sendOne(start + i, row)));
      log(`🏁 完成批次 ${b+1}/${totalBatches}`);
    }

    els.startBtn.disabled = rows.length === 0;
    els.cancelBtn.disabled = true;
    els.exportBtn.disabled = false;
    controller = null;
    log('—— 所有批次执行完成 ——');
  }

  async function handleParse(){
    const file = els.file.files && els.file.files[0];
    if (!file){ alert('请选择 CSV 文件'); return; }
    const text = await file.text();
    rows = parseCSV(text);
    const v = validateHeaders(rows);
    if (!v.ok){
      alert('CSV 缺少必需列：' + v.miss.join(', '));
      rows = [];
      els.startBtn.disabled = true;
      els.previewCard.style.display = 'none';
      updateStats();
      return;
    }
    // 仅保留必需列且去除首尾空白
    rows = rows.map(r => {
      const o = {};
      for (const k of REQUIRED){ o[k] = (r[k] ?? '').toString().trim(); }
      return o;
    });
    renderPreview(rows);
    els.startBtn.disabled = rows.length === 0;
    updateStats();
    log(`📄 已解析 ${rows.length} 行记录；将以 10 条/批次发送。`);
  }

  function handleExport(){
    const merged = rows.map((r, i) => ({...r,
      status: results[i]?.status || '',
      httpStatus: results[i]?.httpStatus || '',
      error: results[i]?.error || ''
    }));
    const csv = toCSV(merged);
    download('batch_request_results.csv', csv, 'text/csv');
  }

  function handleDownloadTemplate(){
    const head = REQUIRED.join(',') + '\n';
    const sample = 'store001,merchant001,示例描述,120,300\n';
    download('template.csv', head + sample, 'text/csv');
  }

  function cancelAll(){
    if (controller){ controller.abort(); }
    els.cancelBtn.disabled = true;
    els.startBtn.disabled = rows.length === 0;
    els.exportBtn.disabled = results.some(r=>r!=null);
    log('⛔ 已请求取消，正在终止后续批次/请求…');
  }

  // Event bindings
  els.parseBtn.addEventListener('click', handleParse);
  els.startBtn.addEventListener('click', runBatches);
  els.cancelBtn.addEventListener('click', cancelAll);
  els.exportBtn.addEventListener('click', handleExport);
  els.downloadTemplate.addEventListener('click', handleDownloadTemplate);
})();
</script>
</body>
</html>
