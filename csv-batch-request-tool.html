<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV批量请求发送工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            padding: 40px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 32px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 30px;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
            border-color: #764ba2;
        }

        input[type="file"] {
            display: none;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 10px 5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .data-preview {
            margin-bottom: 30px;
            display: none;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }

        td {
            padding: 10px 12px;
            border-bottom: 1px solid #eee;
            background: white;
        }

        tr:hover td {
            background: rgba(102, 126, 234, 0.05);
        }

        .progress-container {
            margin-top: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background: #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .status {
            margin-top: 15px;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            background: rgba(102, 126, 234, 0.1);
            color: #333;
        }

        .log-container {
            margin-top: 30px;
            display: none;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .log-entry.success {
            background: linear-gradient(to right, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
            border-left: 3px solid #28a745;
        }

        .log-entry.error {
            background: linear-gradient(to right, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
            border-left: 3px solid #dc3545;
        }

        .log-entry.info {
            background: linear-gradient(to right, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
            border-left: 3px solid #17a2b8;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
            100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
        }

        .processing {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSV批量请求发送工具</h1>
        <p class="subtitle">支持CSV文件导入，自动批量发送GET请求到阿里云日志服务</p>
        
        <div class="warning-box">
            ⚠️ <strong>注意：</strong>此工具直接从浏览器发送请求，可能会遇到CORS（跨域）限制。如遇到跨域错误，请考虑：
            <br>1. 使用浏览器插件临时禁用CORS检查（仅用于测试）
            <br>2. 通过后端服务器代理请求
            <br>3. 确保目标服务器允许跨域请求
        </div>

        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📁</div>
            <p style="font-size: 18px; color: #333; margin-bottom: 10px;">拖拽CSV文件到此处或点击选择</p>
            <p style="color: #999; font-size: 14px;">支持字段：storeSn, merchantSn, description, duration, totalTime</p>
            <input type="file" id="fileInput" accept=".csv">
        </div>

        <div class="data-preview" id="dataPreview">
            <h3 style="color: #333; margin-bottom: 15px;">数据预览（前5条）</h3>
            <div style="overflow-x: auto;">
                <table id="previewTable">
                    <thead>
                        <tr>
                            <th>Store SN</th>
                            <th>Merchant SN</th>
                            <th>Description</th>
                            <th>Duration</th>
                            <th>Total Time</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <p style="margin-top: 15px; color: #666;">总计 <span id="totalRecords" style="color: #667eea; font-weight: bold;">0</span> 条记录，将分 <span id="totalBatches" style="color: #764ba2; font-weight: bold;">0</span> 批发送（每批10条）</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" id="sendBtn" disabled>开始发送请求</button>
            <button class="btn btn-secondary" id="clearBtn">清除数据</button>
        </div>

        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill">0%</div>
            </div>
            <div class="status" id="status">准备就绪...</div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="successCount">0</div>
                    <div class="stat-label">成功</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="errorCount">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="pendingCount">0</div>
                    <div class="stat-label">待处理</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="batchCount">0/0</div>
                    <div class="stat-label">批次进度</div>
                </div>
            </div>
        </div>

        <div class="log-container" id="logContainer">
            <h3 style="color: #333; margin-bottom: 15px;">请求日志</h3>
            <div class="log" id="logContent"></div>
        </div>
    </div>

    <script>
        let csvData = [];
        let isProcessing = false;

        // 文件上传区域事件
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const dataPreview = document.getElementById('dataPreview');
        const sendBtn = document.getElementById('sendBtn');
        const clearBtn = document.getElementById('clearBtn');
        const progressContainer = document.getElementById('progressContainer');
        const logContainer = document.getElementById('logContainer');

        uploadArea.addEventListener('click', () => fileInput.click());

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        clearBtn.addEventListener('click', () => {
            if (isProcessing) {
                alert('正在处理中，请等待完成后再清除');
                return;
            }
            csvData = [];
            fileInput.value = '';
            dataPreview.style.display = 'none';
            progressContainer.style.display = 'none';
            logContainer.style.display = 'none';
            sendBtn.disabled = true;
            document.getElementById('logContent').innerHTML = '';
        });

        sendBtn.addEventListener('click', () => {
            if (!isProcessing && csvData.length > 0) {
                startBatchRequest();
            }
        });

        function handleFile(file) {
            if (!file.name.endsWith('.csv')) {
                alert('请选择CSV文件！');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                parseCSV(e.target.result);
            };
            reader.readAsText(file);
        }

        function parseCSV(content) {
            const lines = content.trim().split('\n');
            if (lines.length < 2) {
                alert('CSV文件内容为空或格式错误！');
                return;
            }

            const headers = lines[0].split(',').map(h => h.trim());
            const requiredHeaders = ['storeSn', 'merchantSn', 'description', 'duration', 'totalTime'];
            
            const headerIndices = {};
            requiredHeaders.forEach(header => {
                const index = headers.findIndex(h => h.toLowerCase() === header.toLowerCase());
                if (index === -1) {
                    alert(`CSV文件缺少必需字段：${header}`);
                    return;
                }
                headerIndices[header] = index;
            });

            csvData = [];
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.trim());
                if (values.length >= headers.length) {
                    csvData.push({
                        storeSn: values[headerIndices.storeSn],
                        merchantSn: values[headerIndices.merchantSn],
                        description: values[headerIndices.description],
                        duration: values[headerIndices.duration],
                        totalTime: values[headerIndices.totalTime]
                    });
                }
            }

            displayPreview();
            sendBtn.disabled = false;
        }

        function displayPreview() {
            const tbody = document.querySelector('#previewTable tbody');
            tbody.innerHTML = '';
            
            const previewCount = Math.min(5, csvData.length);
            for (let i = 0; i < previewCount; i++) {
                const row = tbody.insertRow();
                row.insertCell(0).textContent = csvData[i].storeSn;
                row.insertCell(1).textContent = csvData[i].merchantSn;
                row.insertCell(2).textContent = csvData[i].description;
                row.insertCell(3).textContent = csvData[i].duration;
                row.insertCell(4).textContent = csvData[i].totalTime;
            }

            document.getElementById('totalRecords').textContent = csvData.length;
            document.getElementById('totalBatches').textContent = Math.ceil(csvData.length / 10);
            dataPreview.style.display = 'block';
        }

        async function startBatchRequest() {
            if (isProcessing) return;
            
            isProcessing = true;
            sendBtn.disabled = true;
            sendBtn.classList.add('processing');
            progressContainer.style.display = 'block';
            logContainer.style.display = 'block';
            
            const batchSize = 10;
            const totalBatches = Math.ceil(csvData.length / batchSize);
            let successCount = 0;
            let errorCount = 0;
            
            document.getElementById('logContent').innerHTML = '';
            addLog('info', `开始处理 ${csvData.length} 条记录，分 ${totalBatches} 批发送...`);
            
            for (let i = 0; i < csvData.length; i += batchSize) {
                const batch = csvData.slice(i, Math.min(i + batchSize, csvData.length));
                const batchNum = Math.floor(i / batchSize) + 1;
                
                updateStatus(`正在发送第 ${batchNum}/${totalBatches} 批...`);
                document.getElementById('batchCount').textContent = `${batchNum}/${totalBatches}`;
                document.getElementById('pendingCount').textContent = csvData.length - i - batch.length;
                
                addLog('info', `批次 ${batchNum}: 开始发送 ${batch.length} 条记录`);
                
                const results = await sendBatch(batch);
                
                results.forEach((result, index) => {
                    if (result.success) {
                        successCount++;
                        addLog('success', `✓ 记录 ${i + index + 1}: ${result.message}`);
                    } else {
                        errorCount++;
                        addLog('error', `✗ 记录 ${i + index + 1}: ${result.message}`);
                    }
                });
                
                document.getElementById('successCount').textContent = successCount;
                document.getElementById('errorCount').textContent = errorCount;
                
                const progress = ((i + batch.length) / csvData.length) * 100;
                updateProgress(progress);
                
                // 批次之间延迟，避免请求过快
                if (i + batchSize < csvData.length) {
                    await sleep(1000);
                }
            }
            
            updateStatus(`完成！成功: ${successCount}, 失败: ${errorCount}`);
            addLog('info', `所有请求已完成。成功: ${successCount}, 失败: ${errorCount}`);
            
            isProcessing = false;
            sendBtn.disabled = false;
            sendBtn.classList.remove('processing');
        }

        async function sendBatch(batch) {
            const promises = batch.map(async (record) => {
                try {
                    const params = new URLSearchParams({
                        method: 'dashboard_data_import',
                        platform: 'dashboard',
                        storeSn: record.storeSn,
                        merchantSn: record.merchantSn,
                        description: record.description,
                        duration: record.duration,
                        totalTime: record.totalTime,
                    });
                    
                    const url = `https://mk-group.cn-hangzhou.log.aliyuncs.com/logstores/emenu-mini/track?${params}`;
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'x-log-apiversion': '0.6.0'
                        },
                        mode: 'cors'
                    });
                    
                    if (response.ok) {
                        return {
                            success: true,
                            message: `storeSn: ${record.storeSn} - 状态: ${response.status}`
                        };
                    } else {
                        return {
                            success: false,
                            message: `storeSn: ${record.storeSn} - 错误: ${response.status} ${response.statusText}`
                        };
                    }
                } catch (error) {
                    return {
                        success: false,
                        message: `storeSn: ${record.storeSn} - 错误: ${error.message}`
                    };
                }
            });
            
            return Promise.all(promises);
        }

        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
            progressFill.textContent = Math.round(percentage) + '%';
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function addLog(type, message) {
            const logContent = document.getElementById('logContent');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>