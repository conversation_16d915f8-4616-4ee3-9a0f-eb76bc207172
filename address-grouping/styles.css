/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

#app {
    height: 100%;
    width: 100%;
    position: relative;
}

/* 地图容器样式 */
#map-container {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

/* 控制面板样式 */
.control-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 350px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 20px;
    z-index: 2;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.control-panel h2 {
    margin-bottom: 20px;
    text-align: center;
    color: #409EFF;
}

.setting-item {
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.progress-container {
    margin-top: 20px;
}

.progress-container span {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

/* 上传区域样式 */
.upload-demo {
    margin-bottom: 20px;
}

.el-upload__tip {
    font-size: 12px;
    color: #606266;
}

/* 按钮样式 */
.el-button {
    margin-top: 10px;
    width: 100%;
}

/* 地图标记样式 */
.cluster-marker {
    background-color: rgba(64, 158, 255, 0.7);
    border: 2px solid #409EFF;
    color: white;
    text-align: center;
    padding: 5px;
    border-radius: 50%;
    font-weight: bold;
}

.cluster-rectangle {
    border: 2px solid #409EFF;
    background-color: rgba(64, 158, 255, 0.1);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .control-panel {
        width: 280px;
        right: 10px;
        top: 10px;
    }
}
