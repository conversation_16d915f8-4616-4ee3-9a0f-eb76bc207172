<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址分组工具</title>
    <!-- 引入Element Plus -->
    <link rel="stylesheet" href="https://smart-static.wosaimg.com/libs/element-plus/2.3.8/index.min.css">
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- 地图容器 -->
        <div id="map-container"></div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <h2>地址分组工具</h2>
            
            <!-- 文件上传 -->
            <el-upload
                class="upload-demo"
                drag
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :limit="1"
                accept=".xlsx,.xls">
                <div class="el-upload__text">
                    <i class="el-icon-upload"></i>
                    拖拽文件到此处或 <em>点击上传</em>
                </div>
                <template #tip>
                    <div class="el-upload__tip">
                        请上传Excel文件，表头：门店号、门店名称、省份、城市、区、详细地址
                    </div>
                </template>
            </el-upload>
            
            <!-- 分组设置 -->
            <div class="setting-item">
                <span>分组数量：</span>
                <el-input-number v-model="clusterNum" :min="1" :max="100" size="small"></el-input-number>
            </div>
            
            <!-- 处理按钮 -->
            <el-button type="primary" @click="processFile" :disabled="!selectedFile || processing">开始处理</el-button>
            
            <!-- 导出按钮 -->
            <el-button type="success" @click="exportData" :disabled="!processedData">导出分组结果</el-button>
            
            <!-- 进度条 -->
            <div class="progress-container" v-if="processing">
                <span>{{ progressText }}</span>
                <el-progress :percentage="progress" :status="progressStatus"></el-progress>
            </div>
        </div>
    </div>

    <!-- 引入Vue 3 -->
    <script src="https://smart-static.wosaimg.com/libs/vue/3.3.4/vue.global.min.js"></script>
    <!-- 引入Element Plus -->
    <script src="https://smart-static.wosaimg.com/libs/element-plus/2.3.8/index.full.min.js"></script>
    <!-- 引入xlsx库 -->
    <script src="https://smart-static.wosaimg.com/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- 引入高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=ed8ae3c141ca2df607dd0fe368e4a189"></script>
    <!-- 引入高德UI组件库 -->
    <script src="https://webapi.amap.com/ui/1.1/main.js"></script>
    <!-- 引入自定义脚本 -->
    <script src="app.js"></script>
</body>
</html>
