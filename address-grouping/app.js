// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
    // 初始化Vue应用
    const app = Vue.createApp({
        data() {
            return {
                map: null,                // 高德地图实例
                selectedFile: null,       // 选中的Excel文件
                originalData: [],         // 原始Excel数据
                geocodedData: [],         // 地理编码后的数据
                processedData: null,      // 处理后的数据（包含分组信息）
                clusterNum: 10,           // 分组数量，默认10
                processing: false,        // 是否正在处理
                progress: 0,              // 进度百分比
                progressText: '',         // 进度文本
                progressStatus: '',       // 进度状态
                markers: [],              // 地图标记点
                rectangles: [],           // 地图矩形
                errorAddresses: []        // 解析失败的地址
            };
        },
        mounted() {
            // 初始化高德地图
            this.initMap();
        },
        methods: {
            // 初始化高德地图
            initMap() {
                this.map = new AMap.Map('map-container', {
                    zoom: 4,
                    center: [116.397428, 39.90923], // 默认中心点（北京）
                    viewMode: '3D'
                });
                
                // 添加地图控件
                this.map.plugin([
                    'AMap.Scale',
                    'AMap.ToolBar',
                    'AMap.MapType'
                ], () => {
                    this.map.addControl(new AMap.Scale());
                    this.map.addControl(new AMap.ToolBar());
                    this.map.addControl(new AMap.MapType({
                        defaultType: 0
                    }));
                });
            },
            
            // 处理文件选择
            handleFileChange(file) {
                if (file) {
                    this.selectedFile = file.raw;
                }
            },
            
            // 处理文件
            async processFile() {
                if (!this.selectedFile) {
                    this.$message.error('请先选择Excel文件');
                    return;
                }
                
                try {
                    this.processing = true;
                    this.progress = 0;
                    this.progressText = '正在解析Excel文件...';
                    this.progressStatus = '';
                    
                    // 清除之前的标记和矩形
                    this.clearMapOverlays();
                    
                    // 解析Excel文件
                    await this.parseExcel();
                    
                    // 地理编码
                    await this.geocodeAddresses();
                    
                    // 聚类分析
                    await this.clusterAddresses();
                    
                    // 渲染地图
                    this.renderMap();
                    
                    this.progress = 100;
                    this.progressText = '处理完成';
                    this.progressStatus = 'success';
                    
                    // 输出错误地址到控制台
                    if (this.errorAddresses.length > 0) {
                        console.log('以下地址解析失败：', this.errorAddresses);
                    }
                    
                } catch (error) {
                    console.error('处理文件时出错：', error);
                    this.progressText = '处理失败: ' + error.message;
                    this.progressStatus = 'exception';
                } finally {
                    this.processing = false;
                }
            },
            
            // 解析Excel文件
            async parseExcel() {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            
                            // 获取第一个工作表
                            const firstSheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[firstSheetName];
                            
                            // 转换为JSON
                            const jsonData = XLSX.utils.sheet_to_json(worksheet);
                            
                            // 验证表头
                            const requiredHeaders = ['门店号', '门店名称', '省份', '城市', '区', '详细地址'];
                            const headers = Object.keys(jsonData[0] || {});
                            
                            const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
                            if (missingHeaders.length > 0) {
                                reject(new Error(`Excel文件缺少必要的表头: ${missingHeaders.join(', ')}`));
                                return;
                            }
                            
                            this.originalData = jsonData;
                            resolve(jsonData);
                        } catch (error) {
                            reject(new Error('解析Excel文件失败: ' + error.message));
                        }
                    };
                    
                    reader.onerror = () => {
                        reject(new Error('读取文件失败'));
                    };
                    
                    reader.readAsArrayBuffer(this.selectedFile);
                });
            },
            
            // 地理编码（将地址转换为经纬度）
            async geocodeAddresses() {
                this.progressText = '正在进行地理编码...';
                this.geocodedData = [];
                this.errorAddresses = [];
                
                // 创建地理编码实例
                return new Promise((resolve) => {
                    AMap.plugin('AMap.Geocoder', () => {
                        const geocoder = new AMap.Geocoder({
                            city: "全国", // 城市，默认全国
                            batch: false  // 单条查询，避免返回结果与查询不匹配
                        });
                        
                        this.processGeocode(geocoder).then(resolve);
                    });
                });
            },
            
            // 处理地理编码
            async processGeocode(geocoder) {
                
                // 分批处理，每批10个地址
                const batchSize = 10;
                const totalBatches = Math.ceil(this.originalData.length / batchSize);
                
                for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    const start = batchIndex * batchSize;
                    const end = Math.min(start + batchSize, this.originalData.length);
                    const batch = this.originalData.slice(start, end);
                    
                    // 更新进度
                    this.progress = Math.floor((batchIndex / totalBatches) * 80); // 地理编码占总进度的80%
                    
                    // 处理当前批次
                    await this.processBatch(batch, geocoder);
                    
                    // 添加延迟，避免API调用过于频繁
                    if (batchIndex < totalBatches - 1) {
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }
                }
            },
            
            // 处理一批地址的地理编码
            async processBatch(batch, geocoder) {
                // 确保batch是有效的数组
                if (!Array.isArray(batch) || batch.length === 0) {
                    console.warn('批次数据为空或无效');
                    return;
                }
                
                // 准备有效的地址项
                const validItems = batch.filter(item => item !== null && item !== undefined);
                
                if (validItems.length === 0) {
                    console.warn('没有有效的地址项');
                    return;
                }
                
                // 创建每个地址的查询Promise
                const geocodePromises = validItems.map(item => {
                    // 检查必要的属性是否存在
                    const province = item.省份 || '';
                    const city = item.城市 || '';
                    const district = item.区 || '';
                    const address = item.详细地址 || '';
                    
                    const fullAddress = `${province}${city}${district}${address}`;
                    
                    // 返回一个Promise，封装单条查询
                    return new Promise((resolve) => {
                        geocoder.getLocation(fullAddress, (status, result) => {
                            if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                                // 只取第一个结果
                                const geocode = result.geocodes[0];
                                if (geocode && geocode.location) {
                                    this.geocodedData.push({
                                        ...item,
                                        location: geocode.location,
                                        formattedAddress: geocode.formattedAddress
                                    });
                                } else {
                                    // 记录解析失败的地址
                                    this.errorAddresses.push(fullAddress);
                                }
                            } else {
                                // 记录解析失败的地址
                                this.errorAddresses.push(fullAddress);
                            }
                            resolve();
                        });
                    });
                });
                
                // 使用Promise.all并发处理所有请求
                await Promise.all(geocodePromises);
            },
            
            // 聚类分析
            async clusterAddresses() {
                this.progressText = '正在进行聚类分析...';
                this.progress = 80;
                
                // 如果没有有效的地理编码数据，则返回
                if (this.geocodedData.length === 0) {
                    throw new Error('没有有效的地址数据可供分析');
                }
                
                // 提取坐标点
                const points = this.geocodedData.map(item => [
                    item.location.lng,
                    item.location.lat
                ]);
                
                // 执行K-means聚类
                const clusters = await this.kMeans(points, this.clusterNum);
                
                // 将聚类结果添加到数据中
                this.processedData = this.geocodedData.map((item, index) => {
                    return {
                        ...item,
                        clusterIndex: clusters.assignments[index]
                    };
                });
                
                // 计算每个聚类的边界矩形
                this.calculateClusterBounds(clusters);
                
                this.progress = 90;
            },
            
            // K-means聚类算法
            async kMeans(points, k) {
                return new Promise((resolve) => {
                    // 如果点数少于k，则每个点一个簇
                    if (points.length <= k) {
                        const assignments = points.map((_, i) => i);
                        const centroids = points.map(p => [...p]);
                        resolve({ assignments, centroids });
                        return;
                    }
                    
                    // 随机选择初始中心点
                    let centroids = this.getRandomCentroids(points, k);
                    let assignments = new Array(points.length).fill(-1);
                    let iterations = 0;
                    let changed = true;
                    
                    // 最大迭代次数
                    const maxIterations = 100;
                    
                    // 使用Web Worker进行计算，避免阻塞UI
                    const workerCode = `
                        self.onmessage = function(e) {
                            const { points, centroids, maxIterations } = e.data;
                            let assignments = new Array(points.length).fill(-1);
                            let changed = true;
                            let iterations = 0;
                            
                            while (changed && iterations < maxIterations) {
                                changed = false;
                                iterations++;
                                
                                // 分配点到最近的中心点
                                for (let i = 0; i < points.length; i++) {
                                    const point = points[i];
                                    let minDist = Infinity;
                                    let minIndex = -1;
                                    
                                    for (let j = 0; j < centroids.length; j++) {
                                        const centroid = centroids[j];
                                        const dist = Math.sqrt(
                                            Math.pow(point[0] - centroid[0], 2) + 
                                            Math.pow(point[1] - centroid[1], 2)
                                        );
                                        
                                        if (dist < minDist) {
                                            minDist = dist;
                                            minIndex = j;
                                        }
                                    }
                                    
                                    if (assignments[i] !== minIndex) {
                                        assignments[i] = minIndex;
                                        changed = true;
                                    }
                                }
                                
                                // 重新计算中心点
                                const newCentroids = new Array(centroids.length).fill().map(() => [0, 0]);
                                const counts = new Array(centroids.length).fill(0);
                                
                                for (let i = 0; i < points.length; i++) {
                                    const point = points[i];
                                    const clusterIndex = assignments[i];
                                    
                                    newCentroids[clusterIndex][0] += point[0];
                                    newCentroids[clusterIndex][1] += point[1];
                                    counts[clusterIndex]++;
                                }
                                
                                for (let i = 0; i < centroids.length; i++) {
                                    if (counts[i] > 0) {
                                        centroids[i][0] = newCentroids[i][0] / counts[i];
                                        centroids[i][1] = newCentroids[i][1] / counts[i];
                                    }
                                }
                            }
                            
                            self.postMessage({ assignments, centroids });
                        };
                    `;
                    
                    const blob = new Blob([workerCode], { type: 'application/javascript' });
                    const worker = new Worker(URL.createObjectURL(blob));
                    
                    worker.onmessage = (e) => {
                        const result = e.data;
                        worker.terminate();
                        resolve(result);
                    };
                    
                    worker.postMessage({ points, centroids, maxIterations });
                });
            },
            
            // 随机选择初始中心点
            getRandomCentroids(points, k) {
                const centroids = [];
                const indices = new Set();
                
                // 尝试使用k-means++算法选择初始中心点
                // 选择第一个中心点
                const firstIndex = Math.floor(Math.random() * points.length);
                centroids.push([...points[firstIndex]]);
                indices.add(firstIndex);
                
                // 选择剩余的中心点
                for (let i = 1; i < k; i++) {
                    // 计算每个点到最近中心点的距离
                    const distances = points.map((point, index) => {
                        if (indices.has(index)) return 0;
                        
                        let minDist = Infinity;
                        for (const centroid of centroids) {
                            const dist = Math.sqrt(
                                Math.pow(point[0] - centroid[0], 2) + 
                                Math.pow(point[1] - centroid[1], 2)
                            );
                            minDist = Math.min(minDist, dist);
                        }
                        return minDist;
                    });
                    
                    // 计算概率分布
                    const sum = distances.reduce((a, b) => a + b, 0);
                    if (sum === 0) {
                        // 如果所有点都已经是中心点，随机选择
                        let nextIndex;
                        do {
                            nextIndex = Math.floor(Math.random() * points.length);
                        } while (indices.has(nextIndex));
                        
                        centroids.push([...points[nextIndex]]);
                        indices.add(nextIndex);
                    } else {
                        // 根据距离的平方选择下一个中心点
                        const probs = distances.map(d => d / sum);
                        let r = Math.random();
                        let nextIndex = -1;
                        
                        for (let j = 0; j < probs.length; j++) {
                            if (indices.has(j)) continue;
                            r -= probs[j];
                            if (r <= 0) {
                                nextIndex = j;
                                break;
                            }
                        }
                        
                        if (nextIndex === -1) {
                            // 如果没有选中，选择最远的点
                            let maxDist = -1;
                            for (let j = 0; j < distances.length; j++) {
                                if (!indices.has(j) && distances[j] > maxDist) {
                                    maxDist = distances[j];
                                    nextIndex = j;
                                }
                            }
                        }
                        
                        centroids.push([...points[nextIndex]]);
                        indices.add(nextIndex);
                    }
                }
                
                return centroids;
            },
            
            // 计算每个聚类的边界矩形
            calculateClusterBounds(clusters) {
                // 初始化聚类边界
                this.clusterBounds = new Array(this.clusterNum).fill().map(() => ({
                    minLng: Infinity,
                    maxLng: -Infinity,
                    minLat: Infinity,
                    maxLat: -Infinity,
                    points: []
                }));
                
                // 计算每个聚类的边界
                this.processedData.forEach(item => {
                    const clusterIndex = item.clusterIndex;
                    const lng = item.location.lng;
                    const lat = item.location.lat;
                    
                    this.clusterBounds[clusterIndex].minLng = Math.min(this.clusterBounds[clusterIndex].minLng, lng);
                    this.clusterBounds[clusterIndex].maxLng = Math.max(this.clusterBounds[clusterIndex].maxLng, lng);
                    this.clusterBounds[clusterIndex].minLat = Math.min(this.clusterBounds[clusterIndex].minLat, lat);
                    this.clusterBounds[clusterIndex].maxLat = Math.max(this.clusterBounds[clusterIndex].maxLat, lat);
                    this.clusterBounds[clusterIndex].points.push(item);
                });
            },
            
            // 渲染地图
            renderMap() {
                this.progressText = '正在渲染地图...';
                
                // 清除之前的标记和矩形
                this.clearMapOverlays();
                
                // 创建标记点
                this.createMarkers();
                
                // 创建矩形
                this.createRectangles();
                
                // 调整地图视野以包含所有点
                this.fitMapToMarkers();
                
                this.progress = 95;
            },
            
            // 创建标记点
            createMarkers() {
                // 使用LabelMarker批量创建标记点，提高性能
                const labelMarkers = this.processedData.map(item => {
                    return {
                        name: item.门店名称,
                        position: [item.location.lng, item.location.lat],
                        zooms: [3, 20],
                        opacity: 1,
                        rank: 100,
                        text: {
                            content: `${item.clusterIndex + 1}`,
                            direction: 'center',
                            offset: [0, 0],
                            style: {
                                fontSize: 12,
                                fontWeight: 'normal',
                                fillColor: '#fff',
                                strokeColor: '#fff',
                                strokeWidth: 0
                            }
                        },
                        icon: {
                            type: 'image',
                            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                            size: [18, 18],
                            anchor: 'center'
                        }
                    };
                });
                
                AMap.plugin(['AMap.LabelMarker'], () => {
                    const layer = new AMap.LabelsLayer({
                        zooms: [3, 20],
                        zIndex: 1000,
                        collision: false
                    });
                    
                    this.map.add(layer);
                    
                    // 批量添加标记点
                    const markers = labelMarkers.map(options => {
                        return new AMap.LabelMarker(options);
                    });
                    
                    layer.add(markers);
                    this.markers = markers;
                    
                    // 添加点击事件
                    layer.on('click', (e) => {
                        const marker = e.marker;
                        const position = marker.getPosition();
                        const index = this.processedData.findIndex(item => 
                            item.location.lng === position[0] && 
                            item.location.lat === position[1]
                        );
                        
                        if (index !== -1) {
                            const item = this.processedData[index];
                            // 显示信息窗体
                            const infoWindow = new AMap.InfoWindow({
                                content: `
                                    <div style="padding: 10px;">
                                        <h4>${item.门店名称}</h4>
                                        <p>门店号: ${item.门店号}</p>
                                        <p>地址: ${item.省份}${item.城市}${item.区}${item.详细地址}</p>
                                        <p>分组: ${item.clusterIndex + 1}</p>
                                    </div>
                                `,
                                offset: new AMap.Pixel(0, -30)
                            });
                            
                            infoWindow.open(this.map, position);
                        }
                    });
                });
            },
            
            // 创建矩形
            createRectangles() {
                this.rectangles = this.clusterBounds.map((bound, index) => {
                    // 添加一些边距
                    const padding = 0.01;
                    const southWest = new AMap.LngLat(
                        bound.minLng - padding, 
                        bound.minLat - padding
                    );
                    const northEast = new AMap.LngLat(
                        bound.maxLng + padding, 
                        bound.maxLat + padding
                    );
                    
                    // 创建矩形
                    const rectangle = new AMap.Rectangle({
                        bounds: new AMap.Bounds(southWest, northEast),
                        strokeColor: '#409EFF',
                        strokeWeight: 2,
                        strokeOpacity: 0.8,
                        strokeDasharray: [5, 5],
                        fillColor: '#409EFF',
                        fillOpacity: 0.1,
                        zIndex: 50,
                        cursor: 'pointer'
                    });
                    
                    // 添加标签
                    const center = rectangle.getBounds().getCenter();
                    const label = new AMap.Text({
                        text: `组 ${index + 1}`,
                        position: [center.lng, center.lat],
                        style: {
                            'background-color': '#409EFF',
                            'border-width': '0',
                            'text-align': 'center',
                            'font-size': '14px',
                            'color': 'white',
                            'padding': '5px 10px',
                            'border-radius': '3px'
                        },
                        zIndex: 101
                    });
                    
                    this.map.add([rectangle, label]);
                    return { rectangle, label };
                });
            },
            
            // 调整地图视野以包含所有点
            fitMapToMarkers() {
                if (this.processedData.length > 0) {
                    const points = this.processedData.map(item => [item.location.lng, item.location.lat]);
                    this.map.setFitView(null, false, [50, 50, 50, 50]);
                }
            },
            
            // 清除地图上的标记和矩形
            clearMapOverlays() {
                // 清除矩形
                this.rectangles.forEach(item => {
                    this.map.remove([item.rectangle, item.label]);
                });
                this.rectangles = [];
                
                // 清除标记点
                if (this.markers.length > 0) {
                    const layers = this.map.getAllLayers();
                    const labelLayers = layers.filter(layer => layer instanceof AMap.LabelsLayer);
                    labelLayers.forEach(layer => {
                        layer.clear();
                        this.map.remove(layer);
                    });
                    this.markers = [];
                }
            },
            
            // 导出数据
            exportData() {
                if (!this.processedData) {
                    this.$message.error('没有可导出的数据');
                    return;
                }
                
                try {
                    // 准备导出数据
                    const exportData = this.processedData.map(item => {
                        return {
                            '门店号': item.门店号,
                            '门店名称': item.门店名称,
                            '省份': item.省份,
                            '城市': item.城市,
                            '区': item.区,
                            '详细地址': item.详细地址,
                            '经度': item.location.lng,
                            '纬度': item.location.lat,
                            '分组': item.clusterIndex + 1
                        };
                    });
                    
                    // 创建工作簿
                    const wb = XLSX.utils.book_new();
                    const ws = XLSX.utils.json_to_sheet(exportData);
                    
                    // 添加工作表到工作簿
                    XLSX.utils.book_append_sheet(wb, ws, '地址分组结果');
                    
                    // 生成Excel文件并下载
                    XLSX.writeFile(wb, '地址分组结果.xlsx');
                    
                    this.$message.success('导出成功');
                } catch (error) {
                    console.error('导出数据时出错：', error);
                    this.$message.error('导出失败: ' + error.message);
                }
            }
        }
    });
    
    // 挂载Vue应用
    app.use(ElementPlus).mount('#app');
});
