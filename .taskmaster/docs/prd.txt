# Product Requirements Document: GitLab Code Contribution Statistics Tool

## 1. Overview
**Objective:** Create a Python script to automatically fetch and analyze code contribution statistics for developers from a private GitLab instance (specifically version 15.7.6 CE). The script should be configurable, easy to run, and provide clear, actionable reports.

## 2. Core Features

### 2.1. Configuration
- The script MUST read its configuration from an external file named `config.ini`.
- This file will store:
    - The GitLab instance URL (e.g., `https://gitlab.yourcompany.com`).
    - A GitLab Personal Access Token (PAT) with `read_api` scope.
- A template file `config.ini.template` should be provided to guide the user. Credentials must NOT be hardcoded in the script.

### 2.2. GitLab API Interaction
- The script will communicate with the GitLab v4 API.
- It needs to handle API pagination correctly to retrieve all necessary records (e.g., for projects, users, and commits).

### 2.3. Data Fetching
- The script should be able to fetch all projects accessible by the provided PAT.
- It should fetch all active users from the instance.
- For each user, it will iterate through all projects to fetch their commits within a specified date range.

### 2.4. Commit Statistics
- For each commit, the script must retrieve the detailed statistics, specifically:
    - `additions`: Lines of code added.
    - `deletions`: Lines of code deleted.
    - `total`: Total lines changed.
- The script should calculate the **net contribution** (`additions - deletions`).

### 2.5. Data Aggregation & Reporting
- The script will aggregate the statistics for each developer over the specified period.
- The final output should include:
    - Total Commits
    - Total Additions
    - Total Deletions
    - Net Contribution
- The report must be displayed in two formats:
    1. A clean, human-readable table printed to the console.
    2. A CSV file named `gitlab_code_stats.csv` for easy import into other tools.

### 2.6. Command-Line Interface (CLI)
- The script must be executable from the command line.
- It should accept optional arguments to define the reporting period:
    - `--start-date`: The start date in `YYYY-MM-DD` format. If not provided, defaults to 30 days ago.
    - `--end-date`: The end date in `YYYY-MM-DD` format. If not provided, defaults to today.

## 3. Technology Stack
- **Language:** Python 3
- **Libraries:**
    - `requests`: For making HTTP API calls.
    - `configparser`: For reading the configuration file.
    - (Optional but recommended) `tqdm` for progress bars, `tabulate` for console table formatting.

## 4. Non-Functional Requirements
- **Error Handling:** The script should handle common errors gracefully (e.g., invalid token, network issues, API rate limits).
- **Readability:** The code should be well-commented and follow standard Python conventions (PEP 8).
