{"master": {"tasks": [{"id": 1, "title": "Create Project Structure and Configuration Management", "description": "Set up the basic project structure and implement configuration file handling using configparser.", "details": "1. Create the main script file `gitlab_stats.py`\n2. Create a `config.ini.template` file with placeholders for GitLab URL and PAT\n3. Implement configuration loading using configparser\n4. Add validation for required configuration parameters\n5. Implement error handling for missing or invalid configuration\n\nCode structure:\n```python\nimport configparser\nimport os\n\ndef load_config(config_path='config.ini'):\n    if not os.path.exists(config_path):\n        raise FileNotFoundError(f\"Configuration file not found at {config_path}. Please create one based on config.ini.template\")\n    \n    config = configparser.ConfigParser()\n    config.read(config_path)\n    \n    # Validate required parameters\n    if 'gitlab' not in config:\n        raise ValueError(\"Missing [gitlab] section in config file\")\n    if 'url' not in config['gitlab']:\n        raise ValueError(\"Missing GitLab URL in config file\")\n    if 'token' not in config['gitlab']:\n        raise ValueError(\"Missing GitLab Personal Access Token in config file\")\n    \n    return config\n```", "testStrategy": "1. Create test config files with valid and invalid configurations\n2. Test loading valid configuration\n3. Test error handling for missing file\n4. Test error handling for missing required parameters\n5. Verify the template file contains all necessary placeholders", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Implement Command-Line Interface", "description": "Create a CLI interface that accepts start-date and end-date parameters with appropriate defaults.", "details": "1. Use the `argparse` library to parse command-line arguments\n2. Implement two optional arguments: `--start-date` and `--end-date`\n3. Set default values (30 days ago for start-date, today for end-date)\n4. Validate date format (YYYY-MM-DD)\n5. Convert string dates to Python datetime objects\n\n```python\nimport argparse\nfrom datetime import datetime, timedelta\n\ndef parse_arguments():\n    parser = argparse.ArgumentParser(description='Fetch and analyze GitLab code contribution statistics')\n    \n    # Default dates\n    today = datetime.now().date()\n    thirty_days_ago = today - timedelta(days=30)\n    \n    # Add arguments\n    parser.add_argument('--start-date', type=str, default=thirty_days_ago.strftime('%Y-%m-%d'),\n                        help='Start date in YYYY-MM-DD format (default: 30 days ago)')\n    parser.add_argument('--end-date', type=str, default=today.strftime('%Y-%m-%d'),\n                        help='End date in YYYY-MM-DD format (default: today)')\n    \n    args = parser.parse_args()\n    \n    # Validate and convert dates\n    try:\n        start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date()\n        end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date()\n        \n        if end_date < start_date:\n            raise ValueError(\"End date cannot be earlier than start date\")\n            \n    except ValueError as e:\n        if \"unconverted data remains\" in str(e) or \"does not match format\" in str(e):\n            raise ValueError(\"Dates must be in YYYY-MM-DD format\")\n        else:\n            raise\n    \n    return start_date, end_date\n```", "testStrategy": "1. Test with no arguments (should use defaults)\n2. Test with valid date arguments\n3. Test with invalid date formats\n4. Test with end date earlier than start date\n5. Verify help text is displayed correctly with -h flag", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 3, "title": "Implement GitLab API Client", "description": "Create a GitLab API client class to handle API requests, authentication, and pagination.", "details": "1. Create a GitLabClient class that handles API communication\n2. Implement authentication using the PAT from config\n3. Create methods for API requests with proper error handling\n4. Implement pagination handling to retrieve all results\n5. Add rate limiting awareness\n\n```python\nimport requests\nimport time\nfrom urllib.parse import urljoin\n\nclass GitLabClient:\n    def __init__(self, base_url, token):\n        self.base_url = base_url.rstrip('/')\n        self.token = token\n        self.api_url = urljoin(self.base_url, '/api/v4/')\n        self.headers = {'Private-Token': token}\n    \n    def _make_request(self, endpoint, params=None):\n        \"\"\"Make a single API request\"\"\"\n        url = urljoin(self.api_url, endpoint)\n        response = requests.get(url, headers=self.headers, params=params)\n        \n        if response.status_code == 429:  # Rate limited\n            retry_after = int(response.headers.get('Retry-After', 30))\n            print(f\"Rate limited. Waiting for {retry_after} seconds...\")\n            time.sleep(retry_after)\n            return self._make_request(endpoint, params)  # Retry\n            \n        response.raise_for_status()  # Raise exception for 4XX/5XX responses\n        return response.json()\n    \n    def get_paginated_results(self, endpoint, params=None):\n        \"\"\"Get all pages of results for an endpoint\"\"\"\n        if params is None:\n            params = {}\n        \n        # Set pagination parameters\n        params['per_page'] = 100\n        page = 1\n        all_results = []\n        \n        while True:\n            params['page'] = page\n            results = self._make_request(endpoint, params)\n            \n            if not results:\n                break\n                \n            all_results.extend(results)\n            page += 1\n            \n            # Check if we've reached the last page\n            if len(results) < params['per_page']:\n                break\n                \n        return all_results\n    \n    def test_connection(self):\n        \"\"\"Test the API connection\"\"\"\n        try:\n            self._make_request('version')\n            return True\n        except Exception:\n            return False\n```", "testStrategy": "1. Test connection to GitLab API with valid and invalid tokens\n2. Test pagination by requesting a resource with known multiple pages\n3. Test error handling for various HTTP status codes\n4. Test rate limit handling by mocking rate limit responses\n5. Verify all results are collected when spanning multiple pages", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "Implement Project and User Data Fetching", "description": "Create functions to fetch all accessible projects and active users from the GitLab instance.", "details": "1. Implement a function to fetch all accessible projects\n2. Implement a function to fetch all active users\n3. Add filtering capabilities to exclude archived projects\n4. Add error handling for API failures\n5. Implement caching to avoid redundant API calls\n\n```python\nclass GitLabStatsCollector:\n    def __init__(self, client):\n        self.client = client\n        self.projects_cache = None\n        self.users_cache = None\n    \n    def get_all_projects(self):\n        \"\"\"Fetch all accessible projects\"\"\"\n        if self.projects_cache is not None:\n            return self.projects_cache\n            \n        try:\n            # Fetch only active projects\n            projects = self.client.get_paginated_results('projects', {\n                'archived': False,\n                'statistics': True,\n                'order_by': 'id',\n                'sort': 'asc'\n            })\n            \n            self.projects_cache = projects\n            return projects\n        except Exception as e:\n            raise Exception(f\"Failed to fetch projects: {str(e)}\")\n    \n    def get_all_users(self):\n        \"\"\"Fetch all active users\"\"\"\n        if self.users_cache is not None:\n            return self.users_cache\n            \n        try:\n            # Fetch only active users\n            users = self.client.get_paginated_results('users', {\n                'active': True,\n                'order_by': 'id',\n                'sort': 'asc'\n            })\n            \n            self.users_cache = users\n            return users\n        except Exception as e:\n            raise Exception(f\"Failed to fetch users: {str(e)}\")\n```", "testStrategy": "1. Test fetching projects with mock API responses\n2. Test fetching users with mock API responses\n3. Verify caching works correctly (API is called only once)\n4. Test error handling with simulated API failures\n5. Verify filtering of archived projects works correctly", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement Commit Data Fetching", "description": "Create functions to fetch commit data for each user across all projects within the specified date range.", "details": "1. Implement a function to fetch commits for a specific user in a project\n2. Add date range filtering\n3. Implement detailed commit statistics retrieval\n4. Add progress tracking for long-running operations\n5. Optimize to minimize API calls\n\n```python\nfrom tqdm import tqdm\nfrom datetime import datetime\n\nclass GitLabStatsCollector:\n    # ... (previous methods)\n    \n    def get_user_commits(self, user_id, project_id, start_date, end_date):\n        \"\"\"Fetch commits for a specific user in a specific project within date range\"\"\"\n        try:\n            # Format dates for API\n            start_date_str = start_date.strftime('%Y-%m-%d')\n            end_date_str = end_date.strftime('%Y-%m-%d')\n            \n            # Fetch commits\n            commits = self.client.get_paginated_results(f'projects/{project_id}/repository/commits', {\n                'author_id': user_id,\n                'since': start_date_str,\n                'until': end_date_str,\n                'all': True\n            })\n            \n            return commits\n        except Exception as e:\n            print(f\"Warning: Failed to fetch commits for user {user_id} in project {project_id}: {str(e)}\")\n            return []\n    \n    def get_commit_stats(self, project_id, commit_sha):\n        \"\"\"Fetch detailed statistics for a specific commit\"\"\"\n        try:\n            commit_data = self.client.get_paginated_results(f'projects/{project_id}/repository/commits/{commit_sha}')\n            return {\n                'additions': commit_data.get('stats', {}).get('additions', 0),\n                'deletions': commit_data.get('stats', {}).get('deletions', 0),\n                'total': commit_data.get('stats', {}).get('total', 0)\n            }\n        except Exception as e:\n            print(f\"Warning: Failed to fetch stats for commit {commit_sha} in project {project_id}: {str(e)}\")\n            return {'additions': 0, 'deletions': 0, 'total': 0}\n    \n    def collect_all_user_stats(self, start_date, end_date):\n        \"\"\"Collect commit statistics for all users across all projects\"\"\"\n        users = self.get_all_users()\n        projects = self.get_all_projects()\n        \n        user_stats = {}\n        \n        # Initialize stats for each user\n        for user in users:\n            user_stats[user['id']] = {\n                'username': user['username'],\n                'name': user['name'],\n                'email': user.get('email', ''),\n                'commits': 0,\n                'additions': 0,\n                'deletions': 0,\n                'net_contribution': 0\n            }\n        \n        # Process each project\n        for project in tqdm(projects, desc=\"Processing projects\"):\n            project_id = project['id']\n            \n            # For each user, get their commits in this project\n            for user in users:\n                user_id = user['id']\n                commits = self.get_user_commits(user_id, project_id, start_date, end_date)\n                \n                if not commits:\n                    continue\n                    \n                user_stats[user_id]['commits'] += len(commits)\n                \n                # Get detailed stats for each commit\n                for commit in commits:\n                    stats = self.get_commit_stats(project_id, commit['id'])\n                    user_stats[user_id]['additions'] += stats['additions']\n                    user_stats[user_id]['deletions'] += stats['deletions']\n        \n        # Calculate net contribution\n        for user_id in user_stats:\n            user_stats[user_id]['net_contribution'] = user_stats[user_id]['additions'] - user_stats[user_id]['deletions']\n        \n        return user_stats\n```", "testStrategy": "1. Test commit fetching with mock API responses\n2. Test date filtering to ensure only commits in the specified range are included\n3. Test commit statistics retrieval and aggregation\n4. Test error handling for API failures\n5. Test with a large dataset to verify performance and memory usage", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Implement Console Report Generation", "description": "Create functionality to display the aggregated statistics in a formatted table in the console.", "details": "1. Use the tabulate library to format the statistics as a table\n2. Sort the results by a relevant metric (e.g., total commits)\n3. Format numbers for better readability\n4. Add column headers and totals row\n5. <PERSON>le empty results gracefully\n\n```python\nfrom tabulate import tabulate\n\ndef generate_console_report(user_stats):\n    \"\"\"Generate a formatted console report from user statistics\"\"\"\n    if not user_stats:\n        print(\"No data to display.\")\n        return\n    \n    # Convert dict to list and sort by commits (descending)\n    stats_list = []\n    for user_id, stats in user_stats.items():\n        if stats['commits'] > 0:  # Only include users with commits\n            stats_list.append([\n                stats['name'],\n                stats['username'],\n                stats['email'],\n                stats['commits'],\n                stats['additions'],\n                stats['deletions'],\n                stats['net_contribution']\n            ])\n    \n    # Sort by number of commits (descending)\n    stats_list.sort(key=lambda x: x[3], reverse=True)\n    \n    # Calculate totals\n    total_commits = sum(user['commits'] for user in user_stats.values())\n    total_additions = sum(user['additions'] for user in user_stats.values())\n    total_deletions = sum(user['deletions'] for user in user_stats.values())\n    total_net = total_additions - total_deletions\n    \n    # Add totals row\n    stats_list.append(['TOTAL', '', '', total_commits, total_additions, total_deletions, total_net])\n    \n    # Generate table\n    headers = ['Name', 'Username', 'Email', 'Commits', 'Additions', 'Deletions', 'Net Contribution']\n    table = tabulate(stats_list, headers=headers, tablefmt='grid')\n    \n    print(\"\\nGitLab Code Contribution Statistics:\")\n    print(table)\n```", "testStrategy": "1. Test with various sample datasets including empty, small, and large datasets\n2. Verify sorting works correctly\n3. Verify totals are calculated correctly\n4. Test with mock data containing special characters\n5. Verify formatting is consistent and readable", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement CSV Report Generation", "description": "Create functionality to export the aggregated statistics to a CSV file for further analysis.", "details": "1. Use the csv module to write statistics to a CSV file\n2. Include all relevant fields\n3. Handle file creation errors gracefully\n4. Add a timestamp to the filename (optional)\n5. Ensure proper encoding for special characters\n\n```python\nimport csv\nimport os\nfrom datetime import datetime\n\ndef generate_csv_report(user_stats, filename='gitlab_code_stats.csv'):\n    \"\"\"Generate a CSV report from user statistics\"\"\"\n    if not user_stats:\n        print(f\"No data to write to {filename}.\")\n        return\n    \n    try:\n        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:\n            # Define CSV fields\n            fieldnames = ['Name', 'Username', 'Email', 'Commits', 'Additions', 'Deletions', 'Net Contribution']\n            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)\n            \n            # Write header\n            writer.writeheader()\n            \n            # Write data rows\n            for user_id, stats in user_stats.items():\n                if stats['commits'] > 0:  # Only include users with commits\n                    writer.writerow({\n                        'Name': stats['name'],\n                        'Username': stats['username'],\n                        'Email': stats['email'],\n                        'Commits': stats['commits'],\n                        'Additions': stats['additions'],\n                        'Deletions': stats['deletions'],\n                        'Net Contribution': stats['net_contribution']\n                    })\n            \n            # Write totals row\n            total_commits = sum(user['commits'] for user in user_stats.values())\n            total_additions = sum(user['additions'] for user in user_stats.values())\n            total_deletions = sum(user['deletions'] for user in user_stats.values())\n            total_net = total_additions - total_deletions\n            \n            writer.writerow({\n                'Name': 'TOTAL',\n                'Username': '',\n                'Email': '',\n                'Commits': total_commits,\n                'Additions': total_additions,\n                'Deletions': total_deletions,\n                'Net Contribution': total_net\n            })\n        \n        print(f\"CSV report generated: {os.path.abspath(filename)}\")\n        \n    except Exception as e:\n        print(f\"Error generating CSV report: {str(e)}\")\n```", "testStrategy": "1. Test with various sample datasets\n2. Verify the CSV file is created with correct headers\n3. Verify all data is written correctly including special characters\n4. Test error handling with write-protected directories\n5. Verify the totals row is calculated correctly", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 8, "title": "Implement Error Handling and Logging", "description": "Add comprehensive error handling and logging throughout the application to ensure robustness.", "details": "1. Implement a logging system using Python's built-in logging module\n2. Add appropriate error handling for all potential failure points\n3. Create user-friendly error messages\n4. Add debug logging for troubleshooting\n5. Implement graceful exit on critical errors\n\n```python\nimport logging\nimport sys\n\ndef setup_logging(verbose=False):\n    \"\"\"Set up logging configuration\"\"\"\n    log_level = logging.DEBUG if verbose else logging.INFO\n    \n    # Configure logging\n    logging.basicConfig(\n        level=log_level,\n        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n        handlers=[\n            logging.StreamHandler(sys.stdout)\n        ]\n    )\n    \n    # Create logger\n    logger = logging.getLogger('gitlab_stats')\n    \n    return logger\n\n# Example usage in main function\ndef main():\n    try:\n        # Set up logging\n        logger = setup_logging(verbose=False)\n        \n        # Load configuration\n        logger.info(\"Loading configuration...\")\n        config = load_config()\n        \n        # Parse command line arguments\n        logger.info(\"Parsing command line arguments...\")\n        start_date, end_date = parse_arguments()\n        \n        # Initialize GitLab client\n        logger.info(f\"Connecting to GitLab instance at {config['gitlab']['url']}...\")\n        client = GitLabClient(config['gitlab']['url'], config['gitlab']['token'])\n        \n        # Test connection\n        if not client.test_connection():\n            logger.error(\"Failed to connect to GitLab API. Please check your URL and token.\")\n            sys.exit(1)\n        \n        # Initialize stats collector\n        collector = GitLabStatsCollector(client)\n        \n        # Collect statistics\n        logger.info(f\"Collecting statistics from {start_date} to {end_date}...\")\n        user_stats = collector.collect_all_user_stats(start_date, end_date)\n        \n        # Generate reports\n        logger.info(\"Generating reports...\")\n        generate_console_report(user_stats)\n        generate_csv_report(user_stats)\n        \n        logger.info(\"Done!\")\n        \n    except FileNotFoundError as e:\n        logger.error(f\"Configuration error: {str(e)}\")\n        sys.exit(1)\n    except ValueError as e:\n        logger.error(f\"Validation error: {str(e)}\")\n        sys.exit(1)\n    except requests.exceptions.RequestException as e:\n        logger.error(f\"API request error: {str(e)}\")\n        sys.exit(1)\n    except KeyboardInterrupt:\n        logger.info(\"\\nOperation cancelled by user.\")\n        sys.exit(0)\n    except Exception as e:\n        logger.exception(f\"Unexpected error: {str(e)}\")\n        sys.exit(1)\n```", "testStrategy": "1. Test error handling for various error scenarios\n2. Verify appropriate error messages are displayed\n3. Test logging at different verbosity levels\n4. Verify graceful exit on critical errors\n5. Test handling of keyboard interrupts", "priority": "medium", "dependencies": [3, 4, 5, 6, 7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implement Performance Optimizations", "description": "Optimize the script for performance, especially when dealing with large GitLab instances.", "details": "1. Implement parallel processing for API requests using concurrent.futures\n2. Add caching for frequently accessed data\n3. Optimize API queries to minimize the number of requests\n4. Add batch processing for large datasets\n5. Implement progress tracking for long-running operations\n\n```python\nimport concurrent.futures\nfrom functools import lru_cache\n\nclass GitLabStatsCollector:\n    # ... (previous methods)\n    \n    @lru_cache(maxsize=1000)\n    def get_commit_stats(self, project_id, commit_sha):\n        \"\"\"Fetch detailed statistics for a specific commit with caching\"\"\"\n        # Implementation as before, but now with caching\n    \n    def collect_all_user_stats_parallel(self, start_date, end_date, max_workers=10):\n        \"\"\"Collect commit statistics using parallel processing\"\"\"\n        users = self.get_all_users()\n        projects = self.get_all_projects()\n        \n        user_stats = {}\n        \n        # Initialize stats for each user\n        for user in users:\n            user_stats[user['id']] = {\n                'username': user['username'],\n                'name': user['name'],\n                'email': user.get('email', ''),\n                'commits': 0,\n                'additions': 0,\n                'deletions': 0,\n                'net_contribution': 0\n            }\n        \n        # Function to process a single user-project combination\n        def process_user_project(user, project):\n            user_id = user['id']\n            project_id = project['id']\n            \n            result = {\n                'user_id': user_id,\n                'commits': 0,\n                'additions': 0,\n                'deletions': 0\n            }\n            \n            commits = self.get_user_commits(user_id, project_id, start_date, end_date)\n            if not commits:\n                return result\n                \n            result['commits'] = len(commits)\n            \n            # Get detailed stats for each commit\n            for commit in commits:\n                stats = self.get_commit_stats(project_id, commit['id'])\n                result['additions'] += stats['additions']\n                result['deletions'] += stats['deletions']\n                \n            return result\n        \n        # Create tasks for all user-project combinations\n        tasks = []\n        for user in users:\n            for project in projects:\n                tasks.append((user, project))\n        \n        # Process tasks in parallel\n        results = []\n        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:\n            future_to_task = {executor.submit(process_user_project, user, project): (user, project) \n                             for user, project in tasks}\n            \n            for future in tqdm(concurrent.futures.as_completed(future_to_task), \n                              total=len(tasks), desc=\"Processing user-project combinations\"):\n                task = future_to_task[future]\n                try:\n                    result = future.result()\n                    results.append(result)\n                except Exception as e:\n                    print(f\"Error processing user {task[0]['username']} in project {task[1]['path']}: {str(e)}\")\n        \n        # Aggregate results\n        for result in results:\n            user_id = result['user_id']\n            user_stats[user_id]['commits'] += result['commits']\n            user_stats[user_id]['additions'] += result['additions']\n            user_stats[user_id]['deletions'] += result['deletions']\n        \n        # Calculate net contribution\n        for user_id in user_stats:\n            user_stats[user_id]['net_contribution'] = user_stats[user_id]['additions'] - user_stats[user_id]['deletions']\n        \n        return user_stats\n```", "testStrategy": "1. Benchmark performance with and without optimizations\n2. Test with large datasets to verify scalability\n3. Verify caching works correctly\n4. Test parallel processing with various worker counts\n5. Verify resource usage (CPU, memory) stays within reasonable limits", "priority": "low", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 10, "title": "Create Main Script and Documentation", "description": "Integrate all components into a main script and create comprehensive documentation.", "details": "1. Create the main script entry point\n2. Add docstrings to all functions and classes\n3. Create a README.md with usage instructions\n4. Add examples and sample output\n5. Document all configuration options\n\n```python\n#!/usr/bin/env python3\n\n\"\"\"GitLab Code Contribution Statistics Tool\n\nThis script fetches and analyzes code contribution statistics for developers from a GitLab instance.\n\nUsage:\n    python gitlab_stats.py [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD]\n\nConfiguration:\n    Create a config.ini file based on the provided template with your GitLab URL and token.\n\"\"\"\n\nimport sys\nimport os\n\n# Import all previously implemented modules\n# ...\n\ndef main():\n    \"\"\"Main entry point for the script\"\"\"\n    try:\n        # Set up logging\n        logger = setup_logging(verbose=False)\n        \n        # Load configuration\n        logger.info(\"Loading configuration...\")\n        config = load_config()\n        \n        # Parse command line arguments\n        logger.info(\"Parsing command line arguments...\")\n        start_date, end_date = parse_arguments()\n        \n        # Initialize GitLab client\n        logger.info(f\"Connecting to GitLab instance at {config['gitlab']['url']}...\")\n        client = GitLabClient(config['gitlab']['url'], config['gitlab']['token'])\n        \n        # Test connection\n        if not client.test_connection():\n            logger.error(\"Failed to connect to GitLab API. Please check your URL and token.\")\n            sys.exit(1)\n        \n        # Initialize stats collector\n        collector = GitLabStatsCollector(client)\n        \n        # Collect statistics\n        logger.info(f\"Collecting statistics from {start_date} to {end_date}...\")\n        user_stats = collector.collect_all_user_stats_parallel(start_date, end_date)\n        \n        # Generate reports\n        logger.info(\"Generating reports...\")\n        generate_console_report(user_stats)\n        generate_csv_report(user_stats)\n        \n        logger.info(\"Done!\")\n        \n    except Exception as e:\n        # Error handling as implemented in Task 8\n        pass\n\nif __name__ == \"__main__\":\n    main()\n```\n\nREADME.md content:\n```markdown\n# GitLab Code Contribution Statistics Tool\n\nA Python script to automatically fetch and analyze code contribution statistics for developers from a GitLab instance.\n\n## Features\n\n- Fetch commit statistics for all developers in a GitLab instance\n- Filter by date range\n- Generate console and CSV reports\n- Calculate additions, deletions, and net contribution\n\n## Requirements\n\n- Python 3.6+\n- Required packages: requests, configparser, tqdm, tabulate\n\n## Installation\n\n1. Clone this repository\n2. Install required packages: `pip install -r requirements.txt`\n3. Copy `config.ini.template` to `config.ini` and add your GitLab URL and token\n\n## Usage\n\n```\npython gitlab_stats.py [--start-date YYYY-MM-DD] [--end-date YYYY-MM-DD]\n```\n\n### Arguments\n\n- `--start-date`: Start date in YYYY-MM-DD format (default: 30 days ago)\n- `--end-date`: End date in YYYY-MM-DD format (default: today)\n\n### Configuration\n\nCreate a `config.ini` file with the following structure:\n\n```ini\n[gitlab]\nurl = https://gitlab.yourcompany.com\ntoken = your_personal_access_token\n```\n\n## Output\n\nThe script generates two outputs:\n\n1. A formatted table in the console\n2. A CSV file named `gitlab_code_stats.csv`\n\n## Example Output\n\n```\nGitLab Code Contribution Statistics:\n+---------------+----------+--------------------+---------+-----------+-----------+------------------+\n| Name          | Username | Email              | Commits | Additions | Deletions | Net Contribution |\n+===============+==========+====================+=========+===========+===========+==================+\n| John Doe      | johndoe  | <EMAIL>   | 45      | 2345      | 1234      | 1111             |\n+---------------+----------+--------------------+---------+-----------+-----------+------------------+\n| Jane Smith    | jsmith   | <EMAIL>   | 32      | 1876      | 945       | 931              |\n+---------------+----------+--------------------+---------+-----------+-----------+------------------+\n| TOTAL         |          |                    | 77      | 4221      | 2179      | 2042             |\n+---------------+----------+--------------------+---------+-----------+-----------+------------------+\n```\n```", "testStrategy": "1. Test the main script with various arguments\n2. Verify all components are integrated correctly\n3. Test end-to-end functionality\n4. Review documentation for completeness and accuracy\n5. Verify the script works as expected when installed and run by a new user", "priority": "high", "dependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-07T06:11:30.520Z", "updated": "2025-07-07T06:11:30.520Z", "description": "Tasks for master context"}}}