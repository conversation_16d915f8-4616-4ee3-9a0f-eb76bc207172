<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码数据批量采集工具</title>
    <script src="https://smart-static.wosaimg.com/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            margin: 0 auto;
            background: white;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            height: 100px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
        }

        .header div {
            font-size: 1rem;
            opacity: 0.75;
        }

        .main-content {
            display: flex;
            height: calc(100vh - 100px);
            min-height: 600px;
        }

        .input-section {
            width: 30vw;
            min-width: 300px;
            padding: 30px;
            border-right: 2px solid #f1f5f9;
            background: #fafafa;
        }

        .output-section {
            flex: 1;
            padding: 30px;
            background: white;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        textarea {
            width: 100%;
            height: calc(100% - 180px);
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        textarea:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .controls {
            margin: 20px 0;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .btn:disabled {
            background: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
        }

        .btn-secondary:hover {
            box-shadow: 0 10px 20px rgba(100, 116, 139, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        }

        .btn-success:hover {
            box-shadow: 0 10px 20px rgba(34, 197, 94, 0.3);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 12px 8px;
            font-weight: 600;
            font-size: 13px;
            text-align: left;
        }

        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 12px;
            vertical-align: top;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .data-table .qr-code {
            max-width: 150px;
            word-break: break-all;
            font-family: 'Consolas', monospace;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            transition: width 0.3s ease;
            border-radius: 3px;
        }

        .status-message {
            padding: 10px 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
        }

        .status-info {
            background: #dbeafe;
            color: #1e40af;
            border-left: 4px solid #3b82f6;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
            border-left: 4px solid #22c55e;
        }

        .status-error {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #ef4444;
        }

        .example {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }

        .example-title {
            font-weight: 600;
            color: #334155;
            margin-bottom: 10px;
        }

        .example-item {
            margin: 8px 0;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            color: #64748b;
        }

        .footer {
            background: #f8fafc;
            padding: 20px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .input-section {
                border-right: none;
                border-bottom: 2px solid #f1f5f9;
            }

            .header h1 {
                font-size: 2rem;
            }

            .data-table {
                font-size: 11px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>二维码数据批量采集工具</h1>
        <div>批量获取二维码数据并导出到EXCEL</div>
    </div>

    <div class="main-content">
        <div class="input-section">
            <div class="section-title">
                <svg class="icon" viewBox="0 0 24 24">
                    <path d="M3,11H5V13H3V11M11,5H13V9H11V5M9,11H13V15H9V11M15,11H17V13H15V11M19,5H21V9H19V5M5,5H9V9H5V5M3,19H5V21H3V19M9,19H13V21H9V19M15,19H17V21H15V19Z"/>
                </svg>
                输入二维码URL（每行一个）
            </div>
            <textarea id="inputUrls"
                      placeholder="请输入二维码URL，每行一个，例如：&#10;https://99zhe.com/t/R5IAZ&#10;https://99zhe.com/b/R5IAZ"></textarea>

            <div class="controls">
                <button class="btn" id="fetchBtn" onclick="fetchQRData()">
                    <svg class="icon" viewBox="0 0 24 24" style="width: 16px; height: 16px;">
                        <path d="M17,13H13V17H11V13H7V11H11V7H13V11H17M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    开始采集
                </button>
                <button class="btn btn-secondary" onclick="clearAll()">
                    <svg class="icon" viewBox="0 0 24 24" style="width: 16px; height: 16px;">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                    </svg>
                    清空
                </button>
                <button class="btn btn-success" id="exportBtn" onclick="exportToExcel()" disabled>
                    <svg class="icon" viewBox="0 0 24 24" style="width: 16px; height: 16px;">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    导出
                </button>
            </div>

            <div class="progress-bar" id="progressContainer" style="display: none;">
                <div class="progress-fill" id="progressBar"></div>
            </div>

            <div id="statusMessage"></div>
        </div>

        <div class="output-section">
            <div class="section-title">
                <svg class="icon" viewBox="0 0 24 24">
                    <path d="M19,3H5C3.9,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z"/>
                </svg>
                采集结果
            </div>

            <div style="overflow-x: auto; max-height: calc(100% - 34px); overflow-y: auto;">
                <table class="data-table" id="dataTable">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>二维码</th>
                        <th>二维码编号</th>
                        <th>门店号</th>
                        <th>门店名</th>
                        <th>商户号</th>
                        <th>商户名</th>
                        <th>状态</th>
                    </tr>
                    </thead>
                    <tbody id="dataTableBody">
                    <tr>
                        <td colspan="8" style="text-align: center; color: #94a3b8; padding: 40px;">
                            暂无数据，请先输入二维码URL并点击"开始采集"
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    let collectedData = [];
    let isProcessing = false;

    // 显示状态消息
    function showStatus(message, type = 'info') {
        const statusDiv = document.getElementById('statusMessage');
        statusDiv.className = `status-message status-${type}`;
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';
    }

    // 隐藏状态消息
    function hideStatus() {
        document.getElementById('statusMessage').style.display = 'none';
    }

    // 更新进度条
    function updateProgress(current, total) {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');

        if (total > 0) {
            progressContainer.style.display = 'block';
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
        } else {
            progressContainer.style.display = 'none';
        }
    }

    // 添加数据到表格
    function addRowToTable(index, qrCode, data, status) {
        const tbody = document.getElementById('dataTableBody');

        // 如果是第一条数据，清空提示信息
        if (index === 1) {
            tbody.innerHTML = '';
        }

        const row = tbody.insertRow();
        row.innerHTML = `
                <td>${index}</td>
                <td class="qr-code">${qrCode}</td>
                <td>${data.qr_print_code || '-'}</td>
                <td>${data.storeSn || '-'}</td>
                <td>${data.storeName || '-'}</td>
                <td>${data.merchantSn || '-'}</td>
                <td>${data.merchantName || '-'}</td>
                <td style="color: ${status === '成功' ? '#22c55e' : '#ef4444'}">${status}</td>
            `;
    }

    // 获取二维码数据
    async function fetchQRCodeData(qrUrl) {
        const apiUrl = `https://uf4c-app.shouqianba.com/api/v1/gather/index?url=${encodeURIComponent(qrUrl)}`;

        try {
            const response = await fetch(apiUrl, {
                method: 'GET',
                mode: 'cors',
                headers: {
                    'Accept': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const result = await response.json();

            if (Number(result.code) === 200 && result.data && !result.data.errors) {
                return {
                    success: true,
                    data: {
                        qr_code: result.data.terminal?.qr_code || qrUrl,
                        qr_print_code: result.data.terminal?.qr_print_code || '',
                        storeSn: result.data.store?.storeSn || '',
                        storeName: result.data.store?.storeName || '',
                        merchantSn: result.data.store?.merchantSn || '',
                        merchantName: result.data.store?.merchantName || ''
                    }
                };
            } else {
                throw new Error(result.message || '接口返回错误');
            }
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 将数组分批处理
    function chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    // 批量获取数据
    async function fetchQRData() {
        if (isProcessing) return;

        const inputText = document.getElementById('inputUrls').value.trim();
        if (!inputText) {
            showStatus('请输入二维码URL', 'error');
            return;
        }

        const urls = inputText.split('\n').filter(line => line.trim()).map(line => line.trim());
        if (urls.length === 0) {
            showStatus('请输入有效的二维码URL', 'error');
            return;
        }

        isProcessing = true;
        collectedData = [];
        document.getElementById('fetchBtn').disabled = true;
        document.getElementById('exportBtn').disabled = true;

        showStatus('开始采集数据...', 'info');
        updateProgress(0, urls.length);

        let successCount = 0;
        let errorCount = 0;
        let processedCount = 0;

        // 将URL分成每10个一批
        const batches = chunkArray(urls, 10);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            const batchNumber = batchIndex + 1;

            showStatus(`正在处理第 ${batchNumber}/${batches.length} 批次 (${batch.length} 个二维码)...`, 'info');

            // 批量并发请求
            const batchPromises = batch.map(async (url, index) => {
                const result = await fetchQRCodeData(url);
                const globalIndex = processedCount + index + 1;

                if (result.success) {
                    collectedData.push({
                        原始二维码: url,
                        二维码编号: result.data.qr_print_code,
                        门店号: result.data.storeSn,
                        门店名: result.data.storeName,
                        商户号: result.data.merchantSn,
                        商户名: result.data.merchantName
                    });
                    addRowToTable(globalIndex, url, result.data, '成功');
                    return {success: true, url, result};
                } else {
                    addRowToTable(globalIndex, url, {}, `失败`);
                    return {success: false, url, result};
                }
            });

            // 等待当前批次完成
            const batchResults = await Promise.all(batchPromises);

            // 统计当前批次结果
            batchResults.forEach(result => {
                if (result.success) {
                    successCount++;
                } else {
                    errorCount++;
                }
            });

            processedCount += batch.length;
            updateProgress(processedCount, urls.length);

            // 批次间添加延迟，避免请求过于频繁
            if (batchIndex < batches.length - 1) {
                showStatus(`第 ${batchNumber} 批次完成，等待 1 秒后处理下一批次...`, 'info');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        isProcessing = false;
        document.getElementById('fetchBtn').disabled = false;

        if (successCount > 0) {
            document.getElementById('exportBtn').disabled = false;
            showStatus(`采集完成！成功 ${successCount} 个，失败 ${errorCount} 个`, 'success');
        } else {
            showStatus('采集完成，但没有获取到有效数据', 'error');
        }

        setTimeout(() => {
            updateProgress(0, 0);
        }, 2000);
    }

    // 导出到Excel
    function exportToExcel() {
        if (collectedData.length === 0) {
            showStatus('没有可导出的数据', 'error');
            return;
        }

        try {
            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 创建工作表
            const ws = XLSX.utils.json_to_sheet(collectedData);

            // 设置列宽
            const colWidths = [
                {wch: 40}, // 原始二维码
                {wch: 15}, // 二维码编号
                {wch: 15}, // 门店号
                {wch: 20}, // 门店名
                {wch: 15}, // 商户号
                {wch: 20}  // 商户名
            ];
            ws['!cols'] = colWidths;

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, "二维码数据");

            // 生成文件名
            const now = new Date();
            const timestamp = now.getFullYear() +
                String(now.getMonth() + 1).padStart(2, '0') +
                String(now.getDate()).padStart(2, '0') + '_' +
                String(now.getHours()).padStart(2, '0') +
                String(now.getMinutes()).padStart(2, '0');
            const fileName = `二维码数据_${timestamp}.xlsx`;

            // 导出文件
            XLSX.writeFile(wb, fileName);

            showStatus(`Excel文件已导出: ${fileName}`, 'success');
        } catch (error) {
            showStatus(`导出失败: ${error.message}`, 'error');
        }
    }

    // 清空所有内容
    function clearAll() {
        if (isProcessing) {
            showStatus('正在处理中，无法清空', 'error');
            return;
        }

        document.getElementById('inputUrls').value = '';
        document.getElementById('dataTableBody').innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #94a3b8; padding: 40px;">
                        暂无数据，请先输入二维码URL并点击"开始采集"
                    </td>
                </tr>
            `;
        collectedData = [];
        updateProgress(0, 0);
        hideStatus();
        document.getElementById('exportBtn').disabled = true;
    }

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 为输入框添加快捷键支持
        document.getElementById('inputUrls').addEventListener('keydown', function (e) {
            if (e.ctrlKey && e.key === 'Enter') {
                fetchQRData();
            }
        });

        hideStatus();
    });
</script>
</body>
</html>