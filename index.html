<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址坐标聚类工具</title>
    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.2.47/vue.global.min.js"></script>
    <!-- 引入高德地图API -->
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: '76317e580e8ba2fd846d381584bb2f71',
        }
    </script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=76317e580e8ba2fd846d381584bb2f71"></script>
    <!-- 引入xlsx库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <header>
            <h1>地址坐标聚类工具</h1>
        </header>

        <div class="container">
            <div class="sidebar">
                <div class="panel">
                    <h2>数据导入</h2>
                    <div class="form-group">
                        <label for="fileInput">上传Excel文件：</label>
                        <input type="file" id="fileInput" @change="handleFileUpload" accept=".xlsx,.xls">
                    </div>
                    <div class="form-group" v-if="addresses.length > 0">
                        <p>已导入 {{ addresses.length }} 条地址数据</p>
                    </div>
                    <div class="form-group" v-if="initError">
                        <p style="color: red;">初始化错误: {{ initError }}</p>
                    </div>
                </div>

                <div class="panel" v-if="addresses.length > 0">
                    <h2>聚类设置</h2>
                    <div class="form-group">
                        <label for="clusterCount">分组数量：</label>
                        <input type="number" id="clusterCount" v-model="clusterCount" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <button @click="processAddresses" :disabled="processing">开始处理</button>
                    </div>
                </div>

                <div class="panel" v-if="clusters.length > 0">
                    <h2>分组结果</h2>
                    <div class="cluster-stats">
                        <p>共分为 {{ clusters.length }} 组</p>
                        <ul>
                            <li v-for="(cluster, index) in clusters" :key="index">
                                组 {{ index + 1 }}: {{ cluster.points.length }} 个点
                            </li>
                        </ul>
                    </div>
                    <div class="form-group">
                        <button @click="exportExcel">导出Excel</button>
                    </div>
                </div>
            </div>

            <div class="map-container">
                <div id="map"></div>
                <div class="loading-overlay" v-if="processing">
                    <div class="spinner"></div>
                    <p>{{ processingMessage }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入聚类算法 -->
    <script src="clustering.js"></script>
    <!-- 引入主应用逻辑 -->
    <script src="app.js"></script>
</body>
</html>
