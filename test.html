<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <!-- 引入Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.2.47/vue.global.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        .debug {
            margin-top: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>{{ title }}</h1>
            <p>{{ message }}</p>
            <button @click="changeMessage">点击我</button>
        </div>
    </div>

    <div class="debug">
        <h3>调试信息</h3>
        <div id="debug-content"></div>
    </div>

    <script>
        // 添加调试函数
        function debug(message) {
            console.log(message);
            const debugContent = document.getElementById('debug-content');
            if (debugContent) {
                const p = document.createElement('p');
                p.textContent = message;
                debugContent.appendChild(p);
            }
        }

        debug('页面加载完成');

        // 创建Vue应用
        const app = Vue.createApp({
            data() {
                return {
                    title: '测试页面',
                    message: '这是一个简单的测试页面'
                };
            },
            methods: {
                changeMessage() {
                    this.message = '消息已更改！';
                    debug('消息已更改');
                }
            },
            mounted() {
                debug('Vue应用已挂载');
            }
        });

        // 挂载Vue应用
        try {
            debug('准备挂载Vue应用');
            app.mount('#app');
            debug('Vue应用挂载成功');
        } catch (error) {
            debug('Vue应用挂载失败: ' + error.message);
            console.error('Vue应用挂载失败:', error);
        }
    </script>
</body>
</html>
