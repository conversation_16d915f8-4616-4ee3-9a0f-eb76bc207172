<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量文件下载工具</title>
    <!-- 引入JSZip库用于创建ZIP文件 -->
    <script src="https://cdn.jsdelivr.net/npm/jszip@3.10.1/dist/jszip.min.js"></script>
    <!-- 引入FileSaver.js用于保存文件 -->
    <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <!-- 引入Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.min.js"></script>
    <!-- 引入Element Plus UI库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus@2.3.8/dist/index.css">
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.3.8/dist/index.full.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .file-upload {
            margin-bottom: 20px;
        }

        .upload-area {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f2ff;
        }

        .file-input {
            display: none;
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .upload-desc {
            margin: 8px 0;
            color: #666;
        }

        .format-hint {
            margin-top: 15px;
            padding: 10px;
            color: #666;
        }

        .url-count {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background-color: #e8f5e8;
            border-radius: 5px;
            color: #2e7d32;
        }

        .parsing-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
            padding: 10px;
            background-color: #e3f2fd;
            border-radius: 5px;
            color: #1976d2;
            font-weight: 500;
        }

        kbd {
            background-color: #f1f1f1;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 2px 4px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .url-list {
            margin-top: 20px;
        }

        .url-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 5px;
            background-color: #f9f9f9;
        }

        .url-item.success {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }

        .url-item.error {
            background-color: #ffeaea;
            border-color: #f44336;
        }

        .url-item.downloading {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }

        .url-text {
            flex: 1;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
        }

        .status-icon {
            margin-left: 10px;
            width: 20px;
            height: 20px;
        }

        .progress-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            font-weight: bold;
            color: #333;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .hidden {
            display: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #f44336;
            font-size: 14px;
            margin-top: 10px;
        }

        .success-message {
            color: #4caf50;
            font-size: 14px;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .input-methods {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>批量文件下载工具</h1>
                <p>支持批量下载文件并打包为ZIP格式</p>
            </div>

            <div class="main-content">
                <!-- 文件输入区域 -->
                <div class="input-section">
                    <div class="file-upload">
                        <div class="upload-area" 
                             :class="{ dragover: isDragOver }"
                             @click="$refs.fileInput.click()"
                             @dragover.prevent="isDragOver = true"
                             @dragleave.prevent="isDragOver = false"
                             @drop.prevent="handleFileDrop">
                            <div class="upload-icon">📁</div>
                            <h3>添加下载链接</h3>
                            <p class="upload-desc">点击选择文本文件 或 拖拽文件到此处</p>
                            <p class="upload-desc">也可以在页面任意位置按 <kbd>Ctrl+V</kbd> 粘贴URL列表或文件</p>
                            <div class="format-hint">
                                <small>支持 .txt 文件或直接粘贴文本，每行一个URL地址</small>
                            </div>
                        </div>
                        <input ref="fileInput" type="file" class="file-input" accept=".txt" @change="handleFileSelect">
                    </div>

                    <div v-if="isParsing" class="parsing-status">
                        <div class="loading-spinner"></div>
                        <span>{{ parseMessage }}</span>
                    </div>
                    <div v-if="parseError" class="error-message">{{ parseError }}</div>
                    <div v-if="parseMessage && !isParsing" class="success-message">{{ parseMessage }}</div>
                    <div v-if="urls.length > 0" class="url-count">
                        已解析 <strong>{{ urls.length }}</strong> 个下载链接
                    </div>
                </div>

                <!-- 进度显示 -->
                <div v-if="isDownloading || downloadComplete" class="progress-section">
                    <h3>下载进度</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                    </div>
                    <div class="progress-text">{{ progressText }}</div>
                    
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ completedCount }}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ successCount }}</div>
                            <div class="stat-label">成功</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ errorCount }}</div>
                            <div class="stat-label">失败</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ formatFileSize(totalSize) }}</div>
                            <div class="stat-label">总大小</div>
                        </div>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="controls">
                    <button class="btn btn-primary" 
                            @click="startDownload" 
                            :disabled="urls.length === 0 || isDownloading">
                        <span v-if="isDownloading" class="loading-spinner"></span>
                        {{ isDownloading ? '下载中...' : '开始下载' }}
                    </button>
                    <button class="btn btn-secondary" @click="clearAll" :disabled="isDownloading">
                        清空列表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    urls: [],
                    parseError: '',
                    isDragOver: false,
                    isDownloading: false,
                    downloadComplete: false,
                    completedCount: 0,
                    successCount: 0,
                    errorCount: 0,
                    totalSize: 0,
                    downloadedFiles: [],
                    isParsing: false,
                    parseMessage: ''
                };
            },
            mounted() {
                // 添加全局粘贴事件监听
                document.addEventListener('paste', this.handleGlobalPaste);
            },
            beforeUnmount() {
                // 移除事件监听
                document.removeEventListener('paste', this.handleGlobalPaste);
            },
            computed: {
                progressPercentage() {
                    if (this.urls.length === 0) return 0;
                    return Math.round((this.completedCount / this.urls.length) * 100);
                },
                progressText() {
                    if (!this.isDownloading && !this.downloadComplete) return '';
                    if (this.downloadComplete) {
                        return `下载完成：${this.successCount}/${this.urls.length} 成功`;
                    }
                    return `正在下载：${this.completedCount}/${this.urls.length} (${this.progressPercentage}%)`;
                }
            },
            methods: {
                handleGlobalPaste(event) {
                    // 检查是否在输入框中粘贴，如果是则忽略
                    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                        return;
                    }
                    
                    event.preventDefault();
                    const clipboardData = event.clipboardData || window.clipboardData;
                    
                    // 检查是否有文件
                    const files = clipboardData.files;
                    if (files && files.length > 0) {
                        const file = files[0];
                        // 检查是否是文本文件
                        if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                            this.readTextFile(file);
                            return;
                        } else {
                            this.parseError = '请粘贴文本文件（.txt格式）';
                            return;
                        }
                    }
                    
                    // 如果没有文件，尝试读取文本内容
                    const pastedText = clipboardData.getData('text');
                    if (pastedText.trim()) {
                        this.parseUrlsFromText(pastedText);
                    }
                },
                
                async parseUrlsFromText(text, showProgress = false) {
                    this.parseError = '';
                    
                    if (showProgress) {
                        this.isParsing = true;
                        this.parseMessage = '正在解析文件内容...';
                    }
                    
                    try {
                        if (!text.trim()) {
                            this.urls = [];
                            return;
                        }

                        const lines = text.split('\n').map(line => line.trim()).filter(line => line);
                        
                        if (showProgress) {
                            this.parseMessage = `正在验证 ${lines.length} 行URL地址...`;
                            // 添加小延迟让用户看到状态
                            await new Promise(resolve => setTimeout(resolve, 100));
                        }
                        
                        const validUrls = [];
                        const invalidLines = [];

                        for (let i = 0; i < lines.length; i++) {
                            const line = lines[i];
                            if (this.isValidUrl(line)) {
                                validUrls.push({
                                    url: line,
                                    status: 'pending',
                                    filename: this.extractFilename(line)
                                });
                            } else {
                                invalidLines.push(i + 1);
                            }
                            
                            // 每处理100行显示一次进度
                            if (showProgress && (i + 1) % 100 === 0) {
                                this.parseMessage = `正在验证URL地址... (${i + 1}/${lines.length})`;
                                await new Promise(resolve => setTimeout(resolve, 10));
                            }
                        }

                        if (invalidLines.length > 0) {
                            this.parseError = `第 ${invalidLines.join(', ')} 行不是有效的URL地址`;
                        }

                        this.urls = validUrls;
                        
                        if (showProgress) {
                            this.parseMessage = '';
                        }
                    } finally {
                        if (showProgress) {
                            this.isParsing = false;
                        }
                    }
                },

                isValidUrl(string) {
                    try {
                        new URL(string);
                        return true;
                    } catch (_) {
                        return false;
                    }
                },

                extractFilename(url) {
                    try {
                        const urlObj = new URL(url);
                        const pathname = urlObj.pathname;
                        const filename = pathname.split('/').pop();
                        return filename || `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    } catch (e) {
                        return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    }
                },

                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.readTextFile(file);
                    }
                },

                handleFileDrop(event) {
                    this.isDragOver = false;
                    const files = event.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                            this.readTextFile(file);
                        } else {
                            this.parseError = '请选择.txt格式的文本文件';
                        }
                    }
                },

                readTextFile(file, showProgress = true) {
                    if (showProgress) {
                        this.isParsing = true;
                        this.parseMessage = '正在读取文件...';
                    }
                    
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                        try {
                            await this.parseUrlsFromText(e.target.result, showProgress);
                        } catch (error) {
                            this.parseError = '文件解析失败';
                            if (showProgress) {
                                this.isParsing = false;
                                this.parseMessage = '';
                            }
                        }
                    };
                    reader.onerror = () => {
                        this.parseError = '文件读取失败';
                        if (showProgress) {
                            this.isParsing = false;
                            this.parseMessage = '';
                        }
                    };
                    reader.readAsText(file);
                },

                async startDownload() {
                    if (this.urls.length === 0) return;

                    this.isDownloading = true;
                    this.downloadComplete = false;
                    this.completedCount = 0;
                    this.successCount = 0;
                    this.errorCount = 0;
                    this.totalSize = 0;
                    this.downloadedFiles = [];

                    // 重置所有URL状态
                    this.urls.forEach(url => {
                        url.status = 'pending';
                    });

                    const zip = new JSZip();
                    const maxConcurrent = 3; // 最大并发下载数
                    const chunks = this.chunkArray(this.urls, maxConcurrent);

                    for (const chunk of chunks) {
                        await Promise.all(chunk.map(url => this.downloadFile(url, zip)));
                    }

                    if (this.successCount > 0) {
                        await this.saveZipFile(zip);
                    }

                    this.isDownloading = false;
                    this.downloadComplete = true;
                },

                chunkArray(array, size) {
                    const chunks = [];
                    for (let i = 0; i < array.length; i += size) {
                        chunks.push(array.slice(i, i + size));
                    }
                    return chunks;
                },

                async downloadFile(urlObj, zip) {
                    try {
                        urlObj.status = 'downloading';
                        
                        const response = await fetch(urlObj.url, {
                            method: 'GET',
                            mode: 'cors'
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const blob = await response.blob();
                        const filename = this.getUniqueFilename(urlObj.filename);
                        
                        zip.file(filename, blob);
                        
                        urlObj.status = 'success';
                        this.successCount++;
                        this.totalSize += blob.size;
                        this.downloadedFiles.push({
                            filename,
                            size: blob.size,
                            url: urlObj.url
                        });

                    } catch (error) {
                        console.error(`下载失败: ${urlObj.url}`, error);
                        urlObj.status = 'error';
                        urlObj.error = error.message;
                        this.errorCount++;
                    } finally {
                        this.completedCount++;
                    }
                },

                getUniqueFilename(filename) {
                    const existingNames = this.downloadedFiles.map(f => f.filename);
                    let uniqueName = filename;
                    let counter = 1;

                    while (existingNames.includes(uniqueName)) {
                        const lastDotIndex = filename.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            const name = filename.substring(0, lastDotIndex);
                            const ext = filename.substring(lastDotIndex);
                            uniqueName = `${name}_${counter}${ext}`;
                        } else {
                            uniqueName = `${filename}_${counter}`;
                        }
                        counter++;
                    }

                    return uniqueName;
                },

                async saveZipFile(zip) {
                    try {
                        const content = await zip.generateAsync({ type: 'blob' });
                        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
                        const filename = `批量下载文件_${timestamp}.zip`;
                        saveAs(content, filename);
                    } catch (error) {
                        console.error('保存ZIP文件失败:', error);
                        alert('保存ZIP文件失败: ' + error.message);
                    }
                },

                formatFileSize(bytes) {
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },

                clearAll() {
                    this.urls = [];
                    this.parseError = '';
                    this.isParsing = false;
                    this.parseMessage = '';
                    this.isDownloading = false;
                    this.downloadComplete = false;
                    this.completedCount = 0;
                    this.successCount = 0;
                    this.errorCount = 0;
                    this.totalSize = 0;
                    this.downloadedFiles = [];
                }
            }
        }).mount('#app');
    </script>
</body>
</html>