#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GitLab Code Contribution Statistics Tool

This script fetches commit statistics for users from a GitLab instance and
generates a report in both console and CSV format.
"""

import argparse
import configparser
import csv
import sys
from datetime import datetime, timedelta
import requests
from pathlib import Path

# --- Configuration ---
BASE_DIR = Path(__file__).resolve().parent
CONFIG_FILE = BASE_DIR / 'config.ini'

def get_config():
    """Reads and validates configuration from config.ini."""
    if not CONFIG_FILE.is_file():
        print(f"Error: Configuration file not found at {CONFIG_FILE}")
        print("Please copy config.ini.template to config.ini and fill in your details.")
        sys.exit(1)
    
    config = configparser.ConfigParser()
    config.read(CONFIG_FILE)
    
    try:
        gitlab_url = config.get('gitlab', 'url').rstrip('/')
        gitlab_token = config.get('gitlab', 'token')
        include_users_str = config.get('settings', 'include_users', fallback='').strip()
    except (configparser.NoSectionError, configparser.NoOptionError) as e:
        print(f"Error in configuration file: {e}")
        sys.exit(1)

    if not gitlab_url or not gitlab_token:
        print("Error: GitLab URL and token must be set in config.ini")
        sys.exit(1)
        
    include_users = [user.strip() for user in include_users_str.split(',') if user.strip()]

    return gitlab_url, gitlab_token, include_users

def parse_arguments():
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(description="GitLab Code Contribution Statistics Tool.")
    
    default_start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    default_end_date = datetime.now().strftime('%Y-%m-%d')

    parser.add_argument(
        '--start-date',
        default=default_start_date,
        help=f"Start date for statistics (YYYY-MM-DD). Defaults to {default_start_date}."
    )
    parser.add_argument(
        '--end-date',
        default=default_end_date,
        help=f"End date for statistics (YYYY-MM-DD). Defaults to {default_end_date}."
    )
    
    return parser.parse_args()

class GitLabAPI:
    """A wrapper for the GitLab API."""
    def __init__(self, gitlab_url, private_token):
        self.base_url = f"{gitlab_url}/api/v4"
        self.headers = {'PRIVATE-TOKEN': private_token}
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def _get_paginated(self, url, params=None):
        """Handles pagination for GitLab API requests."""
        items = []
        page = 1
        while True:
            try:
                response = self.session.get(url, params={**(params or {}), 'page': page, 'per_page': 100})
                response.raise_for_status()
                data = response.json()
                if not data:
                    break
                items.extend(data)
                
                if 'x-next-page' not in response.headers or not response.headers['x-next-page']:
                    break
                page = int(response.headers['x-next-page'])
            except requests.exceptions.RequestException as e:
                print(f"API request failed: {e}")
                return None
        return items

    def get_users(self, usernames=None):
        """Get active users. If usernames are provided, fetches specific users."""
        print("Fetching users...")
        if usernames:
            users = []
            for username in usernames:
                user_data = self._get_paginated(f"{self.base_url}/users", params={'username': username})
                if user_data:
                    users.extend(user_data)
            return users
        else:
            return self._get_paginated(f"{self.base_url}/users", params={'active': 'true'})

    def get_projects(self):
        """Get all accessible projects."""
        print("Fetching projects...")
        return self._get_paginated(f"{self.base_url}/projects", params={'membership': 'true', 'simple': 'true'})

    def get_user_commits_for_project(self, project_id, user_email, since, until):
        """Get all commits for a user in a specific project within a date range."""
        params = {
            'author_email': user_email,
            'since': since,
            'until': until,
            'with_stats': 'true'
        }
        return self._get_paginated(f"{self.base_url}/projects/{project_id}/repository/commits", params=params)

from tabulate import tabulate

def generate_reports(stats, start_date, end_date):
    """Generates console and CSV reports."""
    headers = ["Username", "Commits", "Additions", "Deletions", "Net Contribution"]
    
    # Prepare data for tabulation and CSV
    table_data = []
    for username, data in stats.items():
        # Skip users with no activity
        if data['commits'] == 0:
            continue
        
        net_contribution = data['additions'] - data['deletions']
        table_data.append([
            username,
            data['commits'],
            data['additions'],
            data['deletions'],
            net_contribution
        ])

    # Sort by net contribution (descending)
    table_data.sort(key=lambda x: x[4], reverse=True)

    print("\n\n--- Code Contribution Report ---")
    print(f"Period: {start_date} to {end_date}")
    
    # --- Console Report ---
    if not table_data:
        print("\nNo contributions found for the specified period.")
        return
        
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

    # --- CSV Report ---
    csv_filename = BASE_DIR / f'gitlab_code_stats_{start_date}_to_{end_date}.csv'
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            writer.writerows(table_data)
        print(f"\nReport successfully saved to {csv_filename}")
    except IOError as e:
        print(f"\nError writing CSV file: {e}")

def main():
    """Main function to run the statistics tool."""
    args = parse_arguments()
    gitlab_url, gitlab_token, include_users = get_config()

    print("--- GitLab Code Contribution Statistics ---")
    print(f"Period: {args.start_date} to {args.end_date}")
    
    api = GitLabAPI(gitlab_url, gitlab_token)

    users = api.get_users(usernames=include_users if include_users else None)
    if users is None:
        print("\nCould not fetch users. Please check your GitLab connection and token permissions.")
        sys.exit(1)
    
    projects = api.get_projects()
    if projects is None:
        print("\nCould not fetch projects. Please check your GitLab connection and token permissions.")
        sys.exit(1)

    print(f"\nFound {len(users)} active user(s) and {len(projects)} project(s) to analyze.")

    stats = {user['username']: {'commits': 0, 'additions': 0, 'deletions': 0} for user in users}

    # Adjust end_date to include the whole day
    until_date = datetime.strptime(args.end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59).isoformat()

    # --- Main processing loop ---
    project_count = len(projects)
    for i, project in enumerate(projects):
        print(f"\rProcessing project {i+1}/{project_count}: {project['name_with_namespace']}", end="")
        
        user_count = len(users)
        for j, user in enumerate(users):
            # GitLab API uses user's email to filter commits
            user_email = user.get('email')
            if not user_email:
                continue

            commits = api.get_user_commits_for_project(
                project['id'], 
                user_email, 
                args.start_date, 
                until_date
            )

            if commits:
                for commit in commits:
                    stats[user['username']]['commits'] += 1
                    stats[user['username']]['additions'] += commit['stats']['additions']
                    stats[user['username']]['deletions'] += commit['stats']['deletions']
    
    # --- Generate Reports ---
    generate_reports(stats, args.start_date, args.end_date)


if __name__ == '__main__':
    main()


