<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码复杂度分析器测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
        }

        .input-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .input-section h2 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            background: #fafafa;
        }

        .input-format-selector {
            margin-bottom: 15px;
            display: flex;
            gap: 20px;
        }

        .input-format-selector label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .input-format-selector input[type="radio"] {
            margin: 0;
        }

        .buttons {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-sample {
            background: #e74c3c;
            color: white;
            font-size: 12px;
            padding: 8px 12px;
        }

        .btn-sample:hover {
            background: #c0392b;
        }

        .results-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .results-section h2 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .summary h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .summary-item {
            background: #fff;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }

        .summary-item .value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }

        .summary-item .label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .file-list {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .complexity-score {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .complexity-low {
            background: #d4edda;
            color: #155724;
        }

        .complexity-medium {
            background: #fff3cd;
            color: #856404;
        }

        .complexity-high {
            background: #f8d7da;
            color: #721c24;
        }

        .language-tag {
            background: #e9ecef;
            color: #495057;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }

        .file-badges {
            display: flex;
            gap: 5px;
            align-items: center;
            flex-wrap: wrap;
        }

        .badge {
            background: #6c757d;
            color: #fff;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        .badge-new {
            background: #28a745;
        }

        .badge-deleted {
            background: #dc3545;
        }

        .language-distribution {
            margin-top: 15px;
        }

        .lang-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .lang-item:last-child {
            border-bottom: none;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #dc3545;
            margin-bottom: 20px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .summary-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .buttons {
                flex-direction: column;
            }
            
            button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>代码复杂度分析器</h1>
            <p>基于Git Diff分析代码变更的复杂度，支持多种编程语言。输入Git Diff内容，获得绝对复杂度评分。</p>
        </div>

        <div class="input-section">
            <h2>输入数据</h2>
            <div class="input-format-selector">
                <label>
                    <input type="radio" name="inputFormat" value="diff" checked onchange="switchInputFormat()">
                    Git Diff 格式
                </label>
                <label>
                    <input type="radio" name="inputFormat" value="gitlab" onchange="switchInputFormat()">
                    GitLab API 格式
                </label>
            </div>
            
            <textarea id="diffInput" placeholder="请粘贴Git Diff内容...&#10;&#10;示例：&#10;diff --git a/src/main.js b/src/main.js&#10;index 1234567..abcdefg 100644&#10;--- a/src/main.js&#10;+++ b/src/main.js&#10;@@ -1,3 +1,6 @@&#10; function hello() {&#10;+    if (condition) {&#10;+        return 'world';&#10;+    }&#10;     console.log('hello');&#10; }"></textarea>
            
            <div class="buttons">
                <button class="btn-primary" onclick="analyzeCode()">分析复杂度</button>
                <button class="btn-secondary" onclick="clearInput()">清空输入</button>
                <button class="btn-sample" onclick="loadSample('js')">JS示例</button>
                <button class="btn-sample" onclick="loadSample('python')">Python示例</button>
                <button class="btn-sample" onclick="loadSample('java')">Java示例</button>
                <button class="btn-sample" onclick="loadSample('mixed')">混合语言示例</button>
                <button class="btn-sample" onclick="loadSample('gitlab')">GitLab API示例</button>
            </div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <h2>分析结果</h2>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script src="code-complexity-analyzer.js"></script>
    <script>
        const analyzer = new CodeComplexityAnalyzer();

        function switchInputFormat() {
            const format = document.querySelector('input[name="inputFormat"]:checked').value;
            const textarea = document.getElementById('diffInput');
            
            if (format === 'diff') {
                textarea.placeholder = "请粘贴Git Diff内容...\n\n示例：\ndiff --git a/src/main.js b/src/main.js\nindex 1234567..abcdefg 100644\n--- a/src/main.js\n+++ b/src/main.js\n@@ -1,3 +1,6 @@\n function hello() {\n+    if (condition) {\n+        return 'world';\n+    }\n     console.log('hello');\n }";
            } else {
                textarea.placeholder = "请粘贴GitLab API格式的JSON数据...\n\n示例：\n{\n  \"diff\": \"@@ -4,7 +4,7 @@\\n-        <version>2.76.0</version>\\n+        <version>2.77.0</version>\",\n  \"new_path\": \"pom.xml\",\n  \"old_path\": \"pom.xml\",\n  \"new_file\": false,\n  \"deleted_file\": false\n}";
            }
        }

        function analyzeCode() {
            const input = document.getElementById('diffInput').value.trim();
            const format = document.querySelector('input[name="inputFormat"]:checked').value;
            const resultsSection = document.getElementById('resultsSection');
            const resultsContent = document.getElementById('resultsContent');

            if (!input) {
                alert('请输入数据内容');
                return;
            }

            resultsContent.innerHTML = '<div class="loading">分析中...</div>';
            resultsSection.style.display = 'block';

            try {
                setTimeout(() => {
                    let analysisInput;
                    
                    if (format === 'gitlab') {
                        try {
                            analysisInput = JSON.parse(input);
                        } catch (e) {
                            throw new Error('GitLab格式数据JSON解析失败：' + e.message);
                        }
                    } else {
                        analysisInput = input;
                    }
                    
                    const results = analyzer.analyze(analysisInput);
                    console.log(results)
                    displayResults(results);
                }, 100);
            } catch (error) {
                resultsContent.innerHTML = `<div class="error">分析出错: ${error.message}</div>`;
            }
        }

        function displayResults(results) {
            const resultsContent = document.getElementById('resultsContent');
            
            const html = `
                <div class="summary">
                    <h3>总体概览</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <div class="value">${results.totalComplexity}</div>
                            <div class="label">总复杂度</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.totalFiles}</div>
                            <div class="label">总文件数</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.newFiles}</div>
                            <div class="label">新增文件</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.deletedFiles}</div>
                            <div class="label">删除文件</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.modifiedFiles}</div>
                            <div class="label">修改文件</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.totalLinesAdded}</div>
                            <div class="label">新增行数</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.totalLinesRemoved}</div>
                            <div class="label">删除行数</div>
                        </div>
                        <div class="summary-item">
                            <div class="value">${results.summary.filteredFiles}</div>
                            <div class="label">过滤文件</div>
                        </div>
                    </div>
                    
                    <div class="language-distribution">
                        <h4>语言分布</h4>
                        ${Object.entries(results.summary.languageDistribution).map(([lang, data]) => `
                            <div class="lang-item">
                                <span>
                                    <span class="language-tag">${lang}</span>
                                    ${data.files} 个文件
                                </span>
                                <span class="value">${data.complexity.toFixed(2)}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="file-list">
                    <h3>文件详情</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>复杂度</th>
                                <th>语言</th>
                                <th>+行</th>
                                <th>-行</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.files.map(file => `
                                <tr>
                                    <td style="max-width: 300px; word-break: break-all;">${file.fileName}</td>
                                    <td>
                                        <span class="complexity-score ${getComplexityClass(file.complexity)}">
                                            ${file.complexity}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="language-tag">${file.languageType}</span>
                                    </td>
                                    <td style="color: #28a745;">${file.linesAdded}</td>
                                    <td style="color: #dc3545;">${file.linesRemoved}</td>
                                    <td>
                                        <div class="file-badges">
                                            ${file.isNew ? '<span class="badge badge-new">新文件</span>' : ''}
                                            ${file.isDeleted ? '<span class="badge badge-deleted">已删除</span>' : ''}
                                            ${file.reason ? `<span class="badge">${file.reason}</span>` : ''}
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            resultsContent.innerHTML = html;
        }

        function getComplexityClass(complexity) {
            if (complexity === 0) return 'complexity-low';
            if (complexity < 50) return 'complexity-low';
            if (complexity < 150) return 'complexity-medium';
            return 'complexity-high';
        }

        function clearInput() {
            document.getElementById('diffInput').value = '';
            document.getElementById('resultsSection').style.display = 'none';
        }

        function loadSample(type) {
            const samples = {
                js: `diff --git a/src/components/UserCard.js b/src/components/UserCard.js
new file mode 100644
index 0000000..1234567
--- /dev/null
+++ b/src/components/UserCard.js
@@ -0,0 +1,25 @@
+import React, { useState, useEffect } from 'react';
+
+const UserCard = ({ userId, onUpdate }) => {
+    const [user, setUser] = useState(null);
+    const [loading, setLoading] = useState(true);
+
+    useEffect(() => {
+        if (userId) {
+            fetchUser(userId).then(userData => {
+                setUser(userData);
+                setLoading(false);
+            }).catch(error => {
+                console.error('Failed to fetch user:', error);
+                setLoading(false);
+            });
+        }
+    }, [userId]);
+
+    const handleUpdate = async (newData) => {
+        try {
+            const updatedUser = await updateUser(userId, newData);
+            setUser(updatedUser);
+            onUpdate?.(updatedUser);
+        } catch (error) {
+            console.error('Update failed:', error);
+        }
+    };
+
+    return loading ? <div>Loading...</div> : <UserInfo user={user} onUpdate={handleUpdate} />;
+};
+
+export default UserCard;`,

                python: `diff --git a/src/data_processor.py b/src/data_processor.py
index abc1234..def5678 100644
--- a/src/data_processor.py
+++ b/src/data_processor.py
@@ -10,6 +10,8 @@ class DataProcessor:
     def __init__(self, config):
         self.config = config
         self.cache = {}
+        self.retry_count = config.get('retry_count', 3)
+        self.timeout = config.get('timeout', 30)
 
     def process_data(self, data):
         if not data:
@@ -18,10 +20,25 @@ class DataProcessor:
         
         processed = []
         for item in data:
-            if self.validate_item(item):
-                result = self.transform_item(item)
-                processed.append(result)
+            try:
+                if self.validate_item(item):
+                    result = self.transform_item(item)
+                    if result is not None:
+                        processed.append(result)
+                    else:
+                        self.log_warning(f"Transform returned None for item: {item}")
+            except ValidationError as e:
+                self.log_error(f"Validation failed: {e}")
+                continue
+            except Exception as e:
+                self.log_error(f"Unexpected error processing item {item}: {e}")
+                if self.config.get('strict_mode', False):
+                    raise
         
         return processed
+
+    def log_warning(self, message):
+        if self.config.get('verbose', False):
+            print(f"WARNING: {message}")
+
+    def log_error(self, message):
+        print(f"ERROR: {message}")`,

                java: `diff --git a/src/main/java/com/example/UserService.java b/src/main/java/com/example/UserService.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/example/UserService.java
+++ b/src/main/java/com/example/UserService.java
@@ -1,5 +1,8 @@
 package com.example;
 
+import java.util.Optional;
+import java.util.concurrent.CompletableFuture;
+
 public class UserService {
     private UserRepository repository;
     private NotificationService notificationService;
@@ -15,8 +18,23 @@ public class UserService {
             return null;
         }
         
-        User user = repository.findById(id);
-        return user;
+        try {
+            Optional<User> userOptional = repository.findById(id);
+            if (userOptional.isPresent()) {
+                User user = userOptional.get();
+                // 异步发送通知
+                CompletableFuture.runAsync(() -> {
+                    try {
+                        notificationService.notifyUserAccessed(user.getId());
+                    } catch (Exception e) {
+                        logger.warn("Failed to send access notification", e);
+                    }
+                });
+                return user;
+            }
+        } catch (DataAccessException e) {
+            logger.error("Database error while fetching user", e);
+        }
+        return null;
     }
 
     public boolean updateUser(Long id, UserUpdateRequest request) {`,

                mixed: `diff --git a/frontend/src/App.tsx b/frontend/src/App.tsx
new file mode 100644
index 0000000..1234567
--- /dev/null
+++ b/frontend/src/App.tsx
@@ -0,0 +1,20 @@
+import React from 'react';
+import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
+import HomePage from './pages/HomePage';
+import UserPage from './pages/UserPage';
+
+const App: React.FC = () => {
+    return (
+        <Router>
+            <div className="app">
+                <Routes>
+                    <Route path="/" element={<HomePage />} />
+                    <Route path="/user/:id" element={<UserPage />} />
+                </Routes>
+            </div>
+        </Router>
+    );
+};
+
+export default App;
+
diff --git a/backend/main.py b/backend/main.py
index abc1234..def5678 100644
--- a/backend/main.py
+++ b/backend/main.py
@@ -5,6 +5,9 @@ from fastapi.middleware.cors import CORSMiddleware
 app = FastAPI()
 
 app.add_middleware(
     CORSMiddleware,
     allow_origins=["*"],
     allow_methods=["*"],
     allow_headers=["*"],
 )
+
+@app.get("/health")
+async def health_check():
+    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

diff --git a/styles/main.scss b/styles/main.scss
index 1111111..2222222 100644
--- a/styles/main.scss
+++ b/styles/main.scss
@@ -10,4 +10,12 @@
 
 .container {
     max-width: 1200px;
     margin: 0 auto;
+    
+    @media (max-width: 768px) {
+        max-width: 100%;
+        padding: 0 16px;
+    }
 }`
            };

            samples.gitlab = `{
    "diff": "@@ -4,7 +4,7 @@\\n     <parent>\\n         <artifactId>data-report</artifactId>\\n         <groupId>com.wosai.market</groupId>\\n-        <version>2.76.0</version>\\n+        <version>2.77.0</version>\\n     </parent>\\n     <modelVersion>4.0.0</modelVersion>\\n     <groupId>com.wosai.market</groupId>\\n@@ -51,7 +51,7 @@\\n         <dependency>\\n             <groupId>com.wosai.smartbiz</groupId>\\n             <artifactId>payment-sdk-java</artifactId>\\n-            <version>1.24.0</version>\\n+            <version>1.28.2</version>\\n         </dependency>\\n \\n         <dependency>\\n",
    "new_path": "data-report-api/pom.xml",
    "old_path": "data-report-api/pom.xml",
    "a_mode": "100644",
    "b_mode": "100644",
    "new_file": false,
    "renamed_file": false,
    "deleted_file": false
}`;
            
            document.getElementById('diffInput').value = samples[type];
            
            // 如果选择GitLab示例，自动切换到GitLab格式
            if (type === 'gitlab') {
                document.querySelector('input[name="inputFormat"][value="gitlab"]').checked = true;
                switchInputFormat();
            } else {
                document.querySelector('input[name="inputFormat"][value="diff"]').checked = true;
                switchInputFormat();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                analyzeCode();
            }
        });
    </script>
</body>
</html>