<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门店就近分组工具</title>
    <!-- Bootstrap CSS -->
    <link href="https://smart-static.wosaimg.com/libs/twitter-bootstrap/5.3.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- 高德地图 -->
    <script type="text/javascript"
            src="https://webapi.amap.com/maps?v=2.0&key=ed8ae3c141ca2df607dd0fe368e4a189"></script>
    <script type="text/javascript" src="https://webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>
    <!-- XLSX库 -->
    <script src="https://smart-static.wosaimg.com/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --panel-bg: rgba(255, 255, 255, 0.95);
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
        }

        html, body {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        #mapContainer {
            height: 100%;
            width: 100%;
            position: relative;
        }

        #controlsPanel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 340px;
            max-height: calc(100vh - 40px);
            z-index: 1000;
        }

        .card {
            border: none;
            overflow: hidden;
            background: var(--panel-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            backdrop-filter: blur(10px);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            text-align: center;
            padding: 15px;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border: none;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #1e7e34);
            border: none;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .progress-container {
            margin: 15px 0;
            display: none;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }

        .progress-bar {
            border-radius: 4px;
            background: linear-gradient(90deg, var(--primary-color), #0056b3);
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 2px;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .alert {
            border-radius: 8px;
            border: none;
            font-size: 0.9rem;
        }

        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controlsPanel {
                width: calc(100% - 20px);
                left: 10px;
                right: 10px;
                top: 10px;
                max-height: 40vh;
            }
        }

        /* 地图信息窗口样式 */
        .amap-info-window {
            background: rgba(255, 255, 255, .5);
            backdrop-filter: blur(3px);
            border-radius: 8px;
            padding: 10px;
            box-shadow: var(--shadow);
            font-size: 0.9rem;
            min-width: 200px;
        }

        .amap-info-window .store-name {
            display: flex;
            align-items: center;
            font-weight: bolder;
        }

        .amap-info-window .store-name span {
            display: inline-block;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .amap-info-window .store-name strong {
            display: inline-block;
            padding: 0 5px;
            background: #007bff;
            color: white;
            font-size: 80%;
            vertical-align: middle;
            font-weight: 300;
            border-radius: 4px;
            margin-right: 5px;
        }

        .amap-info-window .text-muted {
            font-size: 75%;
        }
    </style>
</head>
<body>
<div id="mapContainer"></div>

<div id="controlsPanel">
    <div class="card mb-3">
        <div class="card-header">
            🗺️ 门店就近分组工具
        </div>
        <div class="card-body">
            <!-- K值设置 -->
            <div class="mb-3">
                <label for="kValue" class="form-label">分组数量 (K):</label>
                <input type="number" class="form-control" id="kValue" value="5" min="1">
                <div class="form-text">建议根据门店数量合理设置分组数</div>
            </div>

            <!-- 文件上传 -->
            <div class="mb-3">
                <label for="excelFile" class="form-label">上传Excel文件:</label>
                <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls">
                <div class="form-text">
                    必需表头：门店号、门店名、省份、城市、区、详细地址、坐标
                </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-container" id="progressContainer">
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <div class="text-center mt-2">
                    <small id="progressText">准备中...</small>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="d-grid gap-2">
                <button class="btn btn-primary" id="clusterBtn" onclick="processAndCluster()">
                    <span class="loading-spinner" id="loadingSpinner"></span>
                    🚀 开始分组
                </button>
                <button class="btn btn-success" id="exportBtn" onclick="exportData()" disabled>
                    📊 导出分组结果
                </button>
            </div>

            <!-- 统计信息 -->
            <div class="stats-grid" id="statsGrid" style="display: none;">
                <div class="stat-item">
                    <div class="stat-value" id="storeCount">0</div>
                    <div class="stat-label">门店总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="groupCount">0</div>
                    <div class="stat-label">分组数量</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 应用状态管理
    class AppState {
        constructor() {
            this.map = null;
            this.labelsLayer = null;
            this.normalMarker = null;
            this.allStoreData = [];
            this.storeLabelMarkers = [];
            this.currentClusterAssignments = [];
            this.originalExcelData = null;
            this.originalHeaders = null;
            this.isProcessing = false;
        }

        reset() {
            this.allStoreData = [];
            this.storeLabelMarkers = [];
            this.currentClusterAssignments = [];
            this.originalExcelData = null;
            this.originalHeaders = null;
            if (this.labelsLayer) {
                this.labelsLayer.clear();
            }
        }
    }

    // 配置常量
    const CONFIG = {
        useGeoDistance: true,
        maxIterations: 300,
        mapCenter: [104.195397, 35.86166],
        mapZoom: 5,
        markerSize: [10, 15],
        colors: [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
            '#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5',
            '#c49c94', '#f7b6d2', '#c7c7c7', '#dbdb8d', '#9edae5'
        ]
    };

    // 工具函数
    const Utils = {
        // 获取分组颜色
        getGroupColor(groupIndex) {
            return CONFIG.colors[groupIndex % CONFIG.colors.length];
        },

        // 显示进度
        showProgress(show, text = '', percent = 0) {
            const container = document.getElementById('progressContainer');
            const bar = document.getElementById('progressBar');
            const textEl = document.getElementById('progressText');

            container.style.display = show ? 'block' : 'none';
            if (show) {
                bar.style.width = `${percent}%`;
                textEl.textContent = text;
            }
        },

        // 显示加载状态
        showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const btn = document.getElementById('clusterBtn');

            spinner.style.display = show ? 'inline-block' : 'none';
            btn.disabled = show;
            appState.isProcessing = show;
        },

        // 更新统计信息
        updateStats(storeCount, groupCount) {
            const statsGrid = document.getElementById('statsGrid');
            const storeCountEl = document.getElementById('storeCount');
            const groupCountEl = document.getElementById('groupCount');

            storeCountEl.textContent = storeCount;
            groupCountEl.textContent = groupCount;
            statsGrid.style.display = 'grid';
        },

        // 显示错误信息
        showError(message) {
            alert(`❌ ${message}`);
            console.error(message);
        },

        // 显示成功信息
        showSuccess(message) {
            console.log(`✅ ${message}`);
        },

        // 防抖函数
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // 距离计算
    const DistanceCalculator = {
        // Haversine公式计算地理距离
        haversine(point1, point2) {
            const toRad = (degree) => degree * Math.PI / 180;
            const R = 6371; // 地球半径，单位km

            const dLat = toRad(point2[1] - point1[1]);
            const dLon = toRad(point2[0] - point1[0]);
            const lat1 = toRad(point1[1]);
            const lat2 = toRad(point2[1]);

            const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

            return R * c;
        },

        // 欧几里得距离
        euclidean(point1, point2) {
            const dx = point1[0] - point2[0];
            const dy = point1[1] - point2[1];
            return Math.sqrt(dx * dx + dy * dy);
        },

        // 根据配置选择距离计算方法
        calculate(point1, point2) {
            return CONFIG.useGeoDistance
                ? this.haversine(point1, point2)
                : this.euclidean(point1, point2);
        }
    };

    // K-means聚类算法
    class KMeans {
        constructor(data, k, maxIterations = CONFIG.maxIterations) {
            this.data = data;
            this.k = k;
            this.maxIterations = maxIterations;
            this.centroids = [];
            this.assignments = [];
        }

        // 初始化质心
        initializeCentroids() {
            if (this.data.length < 100 || this.k > 10) {
                return this.kMeansPlusPlus();
            } else {
                return this.gridBasedInit();
            }
        }

        // K-means++初始化
        kMeansPlusPlus() {
            const centroids = [];

            // 随机选择第一个质心
            const firstCentroid = this.data[Math.floor(Math.random() * this.data.length)];
            centroids.push([...firstCentroid]);

            // 选择其余质心
            for (let i = 1; i < this.k; i++) {
                const distances = this.data.map(point => {
                    let minDist = Infinity;
                    centroids.forEach(centroid => {
                        const dist = DistanceCalculator.calculate(point, centroid);
                        minDist = Math.min(minDist, dist);
                    });
                    return minDist;
                });

                const sumDistances = distances.reduce((a, b) => a + b, 0);
                const randomValue = Math.random() * sumDistances;
                let cumulativeDistance = 0;
                let nextCentroidIndex = -1;

                for (let j = 0; j < distances.length; j++) {
                    cumulativeDistance += distances[j];
                    if (cumulativeDistance >= randomValue) {
                        nextCentroidIndex = j;
                        break;
                    }
                }

                if (nextCentroidIndex === -1) nextCentroidIndex = this.data.length - 1;
                centroids.push([...this.data[nextCentroidIndex]]);
            }

            return centroids;
        }

        // 基于网格的初始化（适用于大量数据点）
        gridBasedInit() {
            // 计算数据边界
            const bounds = this.calculateBounds();
            const gridSize = Math.ceil(Math.sqrt(this.k));
            const lngStep = (bounds.maxLng - bounds.minLng) / gridSize;
            const latStep = (bounds.maxLat - bounds.minLat) / gridSize;

            // 创建网格
            const grid = Array(gridSize).fill().map(() =>
                Array(gridSize).fill().map(() => [])
            );

            // 将点分配到网格
            this.data.forEach(point => {
                const lngIndex = Math.min(gridSize - 1,
                    Math.floor((point[0] - bounds.minLng) / lngStep));
                const latIndex = Math.min(gridSize - 1,
                    Math.floor((point[1] - bounds.minLat) / latStep));
                grid[latIndex][lngIndex].push(point);
            });

            // 从非空网格中选择质心
            const centroids = [];
            for (let i = 0; i < gridSize && centroids.length < this.k; i++) {
                for (let j = 0; j < gridSize && centroids.length < this.k; j++) {
                    if (grid[i][j].length > 0) {
                        const randomIndex = Math.floor(Math.random() * grid[i][j].length);
                        centroids.push([...grid[i][j][randomIndex]]);
                    }
                }
            }

            // 如果需要，用K-means++补充剩余质心
            while (centroids.length < this.k) {
                const distances = this.data.map(point => {
                    let minDist = Infinity;
                    centroids.forEach(centroid => {
                        minDist = Math.min(minDist,
                            DistanceCalculator.calculate(point, centroid));
                    });
                    return minDist;
                });

                const maxDistIndex = distances.indexOf(Math.max(...distances));
                centroids.push([...this.data[maxDistIndex]]);
            }

            return centroids;
        }

        // 计算数据边界
        calculateBounds() {
            let minLng = Infinity, maxLng = -Infinity;
            let minLat = Infinity, maxLat = -Infinity;

            this.data.forEach(point => {
                minLng = Math.min(minLng, point[0]);
                maxLng = Math.max(maxLng, point[0]);
                minLat = Math.min(minLat, point[1]);
                maxLat = Math.max(maxLat, point[1]);
            });

            return {minLng, maxLng, minLat, maxLat};
        }

        // 执行聚类
        async cluster() {
            if (this.data.length === 0 || this.k === 0) return [];

            Utils.showProgress(true, '初始化质心...', 10);
            this.centroids = this.initializeCentroids();
            this.assignments = new Array(this.data.length);

            let oldAssignments;

            for (let iter = 0; iter < this.maxIterations; iter++) {
                const progress = 10 + (iter / this.maxIterations) * 80;
                Utils.showProgress(true, `迭代中... (${iter + 1}/${this.maxIterations})`, progress);

                oldAssignments = [...this.assignments];

                // 分配点到最近的质心
                await this.assignPointsToCentroids();

                // 更新质心
                this.updateCentroids();

                // 检查收敛
                if (this.hasConverged(oldAssignments) && iter > 0) {
                    Utils.showProgress(true, '聚类收敛完成', 100);
                    break;
                }

                // 让UI有机会更新
                if (iter % 10 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 1));
                }
            }

            return this.assignments;
        }

        // 分配点到最近的质心
        async assignPointsToCentroids() {
            for (let i = 0; i < this.data.length; i++) {
                let minDistance = Infinity;
                let closestCentroidIndex = -1;

                for (let j = 0; j < this.k; j++) {
                    const distance = DistanceCalculator.calculate(
                        this.data[i], this.centroids[j]
                    );
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCentroidIndex = j;
                    }
                }

                this.assignments[i] = closestCentroidIndex;
            }
        }

        // 更新质心位置
        updateCentroids() {
            const newCentroids = Array.from({length: this.k}, () => [0, 0]);
            const counts = new Array(this.k).fill(0);

            // 计算每个簇的点的平均位置
            for (let i = 0; i < this.data.length; i++) {
                const centroidIndex = this.assignments[i];
                if (centroidIndex >= 0 && centroidIndex < this.k) {
                    newCentroids[centroidIndex][0] += this.data[i][0];
                    newCentroids[centroidIndex][1] += this.data[i][1];
                    counts[centroidIndex]++;
                }
            }

            // 更新质心位置
            for (let j = 0; j < this.k; j++) {
                if (counts[j] > 0) {
                    this.centroids[j][0] = newCentroids[j][0] / counts[j];
                    this.centroids[j][1] = newCentroids[j][1] / counts[j];
                } else {
                    // 如果某簇为空，重新分配一个远离其他质心的点
                    this.reassignEmptyCluster(j);
                }
            }
        }

        // 重新分配空簇
        reassignEmptyCluster(clusterIndex) {
            const distances = this.data.map(point => {
                let minDistToCentroids = Infinity;
                this.centroids.forEach((centroid, idx) => {
                    if (idx !== clusterIndex) {
                        minDistToCentroids = Math.min(minDistToCentroids,
                            DistanceCalculator.calculate(point, centroid));
                    }
                });
                return minDistToCentroids;
            });

            const farthestPointIndex = distances.indexOf(Math.max(...distances));
            this.centroids[clusterIndex] = [...this.data[farthestPointIndex]];
        }

        // 检查是否收敛
        hasConverged(oldAssignments) {
            if (!oldAssignments) return false;

            for (let i = 0; i < this.assignments.length; i++) {
                if (this.assignments[i] !== oldAssignments[i]) {
                    return false;
                }
            }
            return true;
        }
    }

    // 地图管理器
    class MapManager {
        constructor() {
            this.map = null;
            this.labelsLayer = null;
            this.normalMarker = null;
        }

        // 初始化地图
        initialize() {
            this.map = new AMap.Map('mapContainer', {
                zoom: CONFIG.mapZoom,
                center: CONFIG.mapCenter,
                resizeEnable: true,
                viewMode: '3D',
                mapStyle: 'amap://styles/whitesmoke',
                showLabel: false,
                showIndoorMap: false
            });

            // 创建标签图层
            this.labelsLayer = new AMap.LabelsLayer({
                zooms: [3, 20],
                zIndex: 1000,
                collision: false
            });

            this.map.add(this.labelsLayer);

            // 添加地图控件
            this.addMapControls();

            // 创建普通标记用于信息窗口
            this.normalMarker = new AMap.Marker({
                anchor: 'bottom-center',
                offset: [0, -15],
            });

            return this.map;
        }

        // 添加地图控件
        addMapControls() {
            AMap.plugin(['AMap.ToolBar', 'AMap.Scale', 'AMap.HawkEye'], () => {
                this.map.addControl(new AMap.ToolBar());
                this.map.addControl(new AMap.Scale());
                this.map.addControl(new AMap.HawkEye({isOpen: false}));
            });
        }

        // 创建标记
        createMarkers(storeData) {
            const markers = [];

            storeData.forEach((store, index) => {
                const icon = {
                    type: 'image',
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                    size: CONFIG.markerSize,
                    anchor: 'bottom-center',
                };

                const labelMarker = new AMap.LabelMarker({
                    position: new AMap.LngLat(store.coords[0], store.coords[1]),
                    icon: icon,
                    rank: 100,
                    extData: {
                        storeIndex: index,
                        groupIndex: -1,
                        defaultColor: '#007bff',
                        storeName: store.storeName,
                        storeId: store.storeId,
                        address: store.address
                    }
                });

                // 绑定事件
                labelMarker.on('mouseover', this.handleMarkerMouseOver.bind(this));
                labelMarker.on('mouseout', this.handleMarkerMouseOut.bind(this));

                markers.push(labelMarker);
            });

            this.labelsLayer.add(markers);
            return markers;
        }

        // 处理标记鼠标悬停
        handleMarkerMouseOver(e) {
            const currentMarker = e.target;
            const extData = currentMarker.getExtData();
            const currentGroupIndex = extData.groupIndex;
            const position = currentMarker.getPosition();

            // 显示信息窗口
            this.showInfoWindow(position, extData, currentGroupIndex);

            if (currentGroupIndex === -1) {
                currentMarker.setRank(200);
                return;
            }

            // 高亮同组标记
            this.highlightGroupMarkers(currentGroupIndex);
        }

        // 处理标记鼠标移出
        handleMarkerMouseOut(e) {
            const currentMarker = e.target;
            const extData = currentMarker.getExtData();
            const currentGroupIndex = extData.groupIndex;

            // 隐藏信息窗口
            this.map.remove(this.normalMarker);

            if (currentGroupIndex === -1) {
                currentMarker.setRank(100);
                return;
            }

            // 恢复标记样式
            this.restoreGroupMarkers(currentGroupIndex);
        }

        // 显示信息窗口
        showInfoWindow(position, extData, groupIndex) {
            const content = `
                    <div class="amap-info-window">
                        <div class="store-name">
                            <strong>${groupIndex + 1}</strong>
                            <span>${extData.storeName}</span>
                        </div>
                        <div class="text-muted">${extData.address}</div>
                    </div>
                `;

            this.normalMarker.setContent(content);
            this.normalMarker.setPosition(position);
            this.map.add(this.normalMarker);
        }

        // 高亮同组标记
        highlightGroupMarkers(groupIndex) {
            appState.storeLabelMarkers.forEach(marker => {
                const markerExtData = marker.getExtData();
                if (markerExtData.groupIndex === groupIndex) {
                    marker.setIcon({
                        type: 'image',
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                        size: CONFIG.markerSize,
                        anchor: 'bottom-center',
                    });
                    marker.setRank(200);
                } else {
                    marker.setRank(100);
                }
            });
        }

        // 恢复同组标记样式
        restoreGroupMarkers(groupIndex) {
            appState.storeLabelMarkers.forEach(marker => {
                const markerExtData = marker.getExtData();
                if (markerExtData.groupIndex === groupIndex) {
                    marker.setIcon({
                        type: 'image',
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                        size: CONFIG.markerSize,
                        anchor: 'bottom-center',
                    });
                }
                marker.setRank(100);
            });
        }

        // 更新标记分组信息
        updateMarkersWithGroups(assignments) {
            assignments.forEach((clusterIdx, dataIdx) => {
                if (appState.storeLabelMarkers[dataIdx]) {
                    const groupColor = Utils.getGroupColor(clusterIdx);
                    const extData = appState.storeLabelMarkers[dataIdx].getExtData();

                    appState.storeLabelMarkers[dataIdx].setExtData({
                        ...extData,
                        groupIndex: clusterIdx,
                        defaultColor: groupColor
                    });
                }
            });
        }

        // 调整地图视野
        fitView() {
            if (appState.storeLabelMarkers.length > 0) {
                this.map.setFitView(appState.storeLabelMarkers, false, [60, 100, 60, 380], 18);
            }
        }

        // 清除所有标记
        clear() {
            if (this.labelsLayer) {
                this.labelsLayer.clear();
            }
        }
    }

    // Excel数据处理器
    class ExcelProcessor {
        // 验证文件
        static validateFile(file) {
            if (!file) {
                throw new Error('请先上传Excel文件！');
            }

            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];

            if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
                throw new Error('请上传有效的Excel文件（.xlsx或.xls格式）！');
            }
        }

        // 处理Excel文件
        static async processFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();

                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, {type: 'array'});
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

                        resolve(jsonData);
                    } catch (error) {
                        reject(new Error(`Excel文件解析失败: ${error.message}`));
                    }
                };

                reader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };

                reader.readAsArrayBuffer(file);
            });
        }

        // 解析门店数据
        static parseStoreData(jsonData) {
            if (jsonData.length < 2) {
                throw new Error('Excel文件内容为空或表头不正确！');
            }

            const headers = jsonData[0].map(header => String(header).trim());
            const requiredColumns = ['坐标', '门店号', '门店名', '详细地址'];
            const columnIndices = {};

            // 检查必需列
            requiredColumns.forEach(col => {
                const index = headers.indexOf(col);
                if (index === -1 && col === '坐标') {
                    throw new Error(`Excel文件中未找到"${col}"列！`);
                }
                columnIndices[col] = index;
            });

            const storeData = [];
            const coordinates = [];

            // 解析数据行
            for (let i = 1; i < jsonData.length; i++) {
                const row = jsonData[i];
                if (!row || !row[columnIndices['坐标']]) continue;

                try {
                    const coordString = String(row[columnIndices['坐标']]);
                    const coords = this.parseCoordinates(coordString);

                    if (coords) {
                        coordinates.push(coords);
                        storeData.push({
                            originalIndex: storeData.length,
                            coords: coords,
                            storeId: this.getColumnValue(row, columnIndices['门店号'], 'N/A'),
                            storeName: this.getColumnValue(row, columnIndices['门店名'], `门店 ${storeData.length + 1}`),
                            address: this.getColumnValue(row, columnIndices['详细地址'], '')
                        });
                    }
                } catch (error) {
                    console.warn(`第 ${i + 1} 行数据解析失败: ${error.message}`);
                }
            }

            if (coordinates.length === 0) {
                throw new Error('未能从文件中解析出任何有效的坐标数据！');
            }

            return {storeData, coordinates, headers, jsonData};
        }

        // 解析坐标字符串
        static parseCoordinates(coordString) {
            const parts = coordString.split(/[,，]/);
            if (parts.length !== 2) {
                throw new Error(`坐标格式无效: ${coordString}`);
            }

            const lng = parseFloat(parts[0].trim());
            const lat = parseFloat(parts[1].trim());

            if (isNaN(lng) || isNaN(lat)) {
                throw new Error(`坐标数值无效: ${coordString}`);
            }

            // 验证坐标范围（中国大致范围）
            if (lng < 73 || lng > 135 || lat < 3 || lat > 54) {
                console.warn(`坐标可能超出中国范围: ${coordString}`);
            }

            return [lng, lat];
        }

        // 获取列值
        static getColumnValue(row, columnIndex, defaultValue) {
            return columnIndex !== -1 && row[columnIndex] !== undefined
                ? row[columnIndex]
                : defaultValue;
        }

        // 导出分组结果
        static exportResults(originalData, headers, assignments) {
            try {
                const exportData = JSON.parse(JSON.stringify(originalData));

                // 添加分组列
                exportData[0].push("分组");

                // 为每行添加分组信息
                for (let i = 1; i < exportData.length; i++) {
                    const assignmentIndex = i - 1;
                    if (assignmentIndex < assignments.length) {
                        const groupIndex = assignments[assignmentIndex];
                        exportData[i].push(groupIndex >= 0 ? `第${groupIndex + 1}组` : "未分组");
                    } else {
                        exportData[i].push("未分组");
                    }
                }

                // 创建工作簿
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(exportData);

                // 设置列宽
                const colWidths = headers.map(() => ({wch: 15}));
                colWidths.push({wch: 10}); // 分组列
                ws['!cols'] = colWidths;

                XLSX.utils.book_append_sheet(wb, ws, "分组结果");

                // 生成文件名
                const timestamp = new Date().toISOString()
                    .replace(/[:.]/g, "-")
                    .substring(0, 19);
                const fileName = `门店分组结果_${timestamp}.xlsx`;

                XLSX.writeFile(wb, fileName);
                Utils.showSuccess(`导出成功: ${fileName}`);

            } catch (error) {
                throw new Error(`导出失败: ${error.message}`);
            }
        }
    }

    // 全局应用状态
    const appState = new AppState();
    let mapManager;

    // 初始化应用
    document.addEventListener('DOMContentLoaded', function () {
        try {
            mapManager = new MapManager();
            appState.map = mapManager.initialize();
            appState.labelsLayer = mapManager.labelsLayer;
            appState.normalMarker = mapManager.normalMarker;

            Utils.showSuccess('地图初始化完成');
        } catch (error) {
            Utils.showError(`地图初始化失败: ${error.message}`);
        }
    });

    // 主要处理函数
    async function processAndCluster() {
        if (appState.isProcessing) {
            Utils.showError('正在处理中，请稍候...');
            return;
        }

        try {
            // 获取参数
            const k = parseInt(document.getElementById('kValue').value);

            const file = document.getElementById('excelFile').files[0];
            ExcelProcessor.validateFile(file);

            // 开始处理
            Utils.showLoading(true);
            Utils.showProgress(true, '读取Excel文件...', 5);

            // 重置状态
            appState.reset();
            mapManager.clear();

            // 处理Excel文件
            const jsonData = await ExcelProcessor.processFile(file);
            Utils.showProgress(true, '解析门店数据...', 15);

            const {storeData, coordinates, headers} = ExcelProcessor.parseStoreData(jsonData);

            // 验证数据
            if (coordinates.length < k) {
                throw new Error(`门店数量 (${coordinates.length}) 少于分组数量 (${k})，请调整K值或检查数据。`);
            }

            // 保存数据
            appState.allStoreData = storeData;
            appState.originalExcelData = jsonData;
            appState.originalHeaders = headers;

            Utils.showProgress(true, '创建地图标记...', 25);

            // 创建地图标记
            appState.storeLabelMarkers = mapManager.createMarkers(storeData);

            Utils.showProgress(true, '执行K-means聚类...', 35);

            // 执行聚类
            const kmeans = new KMeans(coordinates, k);
            const startTime = performance.now();
            appState.currentClusterAssignments = await kmeans.cluster();
            const endTime = performance.now();

            Utils.showProgress(true, '更新地图显示...', 95);

            // 更新地图标记
            mapManager.updateMarkersWithGroups(appState.currentClusterAssignments);

            // 调整地图视野
            mapManager.fitView();

            // 更新UI
            Utils.updateStats(storeData.length, k);
            document.getElementById('exportBtn').disabled = false;

            Utils.showProgress(false);
            Utils.showSuccess(`聚类完成！耗时: ${(endTime - startTime).toFixed(2)}ms`);

        } catch (error) {
            Utils.showError(error.message);
            Utils.showProgress(false);
        } finally {
            Utils.showLoading(false);
        }
    }

    // 导出数据函数
    async function exportData() {
        if (!appState.originalExcelData || !appState.currentClusterAssignments ||
            appState.currentClusterAssignments.length === 0) {
            Utils.showError('没有可导出的数据，请先上传Excel文件并执行分组！');
            return;
        }

        try {
            Utils.showLoading(true);

            await ExcelProcessor.exportResults(
                appState.originalExcelData,
                appState.originalHeaders,
                appState.currentClusterAssignments
            );

        } catch (error) {
            Utils.showError(error.message);
        } finally {
            Utils.showLoading(false);
        }
    }

    // 文件选择事件处理
    document.getElementById('excelFile').addEventListener('change', function (e) {
        const file = e.target.files[0];
        if (file) {
            // 重置导出按钮状态
            document.getElementById('exportBtn').disabled = true;
            document.getElementById('statsGrid').style.display = 'none';
        }
    });

    // 键盘快捷键支持
    document.addEventListener('keydown', function (e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'Enter':
                    e.preventDefault();
                    if (!appState.isProcessing) {
                        processAndCluster();
                    }
                    break;
                case 's':
                    e.preventDefault();
                    if (!document.getElementById('exportBtn').disabled) {
                        exportData();
                    }
                    break;
            }
        }
    });

    // 窗口大小变化处理
    window.addEventListener('resize', Utils.debounce(() => {
        if (appState.map) {
            appState.map.getSize();
        }
    }, 250));

    // 防止页面意外关闭时丢失数据
    window.addEventListener('beforeunload', function (e) {
        if (appState.isProcessing) {
            e.preventDefault();
            e.returnValue = '正在处理数据，确定要离开吗？';
            return e.returnValue;
        }
    });
</script>
</body>
</html>