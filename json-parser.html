<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON 数据解析与可视化工具</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/jsonpath/1.1.1/jsonpath.min.js"></script>
    <style>
        :root {
            --primary-color: #007bff;
            --border-color: #dee2e6;
            --background-color: #f8f9fa;
            --panel-background: #ffffff;
            --text-color: #212529;
            --text-light-color: #6c757d;
            --danger-color: #dc3545;
            --success-color: #28a745;
        }
        body, html {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100%;
            background-color: var(--background-color);
            color: var(--text-color);
            overflow: hidden;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .settings-panel {
            width: 450px;
            flex-shrink: 0;
            background-color: var(--panel-background);
            border-right: 1px solid var(--border-color);
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        .settings-panel h3 {
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
            font-size: 18px;
        }
        .field-config-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .field-config-row input {
            flex-grow: 1;
            margin-right: 8px;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }
        .field-config-row .icon-button {
            cursor: pointer;
            border: none;
            background: none;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .icon-button svg {
            width: 20px;
            height: 20px;
        }
        .add-field-btn svg { fill: var(--primary-color); }
        .remove-field-btn svg { fill: var(--danger-color); }

        .data-panel {
            flex-grow: 1;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .data-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-shrink: 0;
        }
        .data-panel-header h3 {
            margin: 0;
            font-size: 18px;
        }
        .data-panel-header .actions button, .data-panel-header .actions label {
            margin-left: 10px;
            padding: 8px 15px;
            border: 1px solid var(--primary-color);
            background-color: var(--primary-color);
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .data-panel-header .actions button:hover, .data-panel-header .actions label:hover {
            background-color: #0056b3;
        }
        .data-panel-header .actions .clear-btn {
            background-color: transparent;
            color: var(--danger-color);
            border-color: var(--danger-color);
        }
        .data-panel-header .actions .clear-btn:hover {
            background-color: var(--danger-color);
            color: white;
        }
        #file-upload {
            display: none;
        }
        .data-display {
            flex-grow: 1;
            overflow: auto;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            background-color: var(--panel-background);
            position: relative;
        }
        .placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
            color: var(--text-light-color);
            font-size: 16px;
            line-height: 1.6;
        }
        table {
            width: 100%;
            font-size: 13px;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }
        thead th {
            background-color: var(--background-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }
        tbody tr:hover {
            background-color: #f1f1f1;
        }
        .drag-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 123, 255, 0.2);
            border: 3px dashed var(--primary-color);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            color: var(--primary-color);
            font-weight: bold;
            z-index: 9999;
            pointer-events: none; /* Important */
        }
    </style>
</head>
<body>

    <div class="container">
        <aside class="settings-panel">
            <h3>字段提取设置</h3>

            <!-- 列表路径配置区域 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color);">
                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: var(--text-light-color);">列表路径配置（可选，用于提取嵌套数组）</h4>
                <div style="display: flex; gap: 8px;">
                    <input type="text" id="list-path" placeholder="示例：$.data[*]" style="flex: 2; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;">
                </div>
            </div>

            <!-- 主键配置区域 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color);">
                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: var(--text-light-color);">主键配置（可选，用于数据去重）</h4>
                <div style="display: flex; gap: 8px;">
                    <input type="text" id="primary-key-path" placeholder="示例：$.id" style="flex: 2; padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;">
                </div>
            </div>

            <div id="field-config-container">
                <div class="field-config-row">
                    <input type="text" class="field-name" placeholder="字段名称">
                    <input type="text" class="field-path" placeholder="字段路径">
                    <button class="icon-button add-field-btn" onclick="addField()">
                        <svg viewBox="0 0 24 24"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path></svg>
                    </button>
                </div>
            </div>
        </aside>

        <main class="data-panel">
            <div class="data-panel-header">
                <h3>数据展示</h3>
                <div class="actions">
                    <label for="file-upload" class="button">上传JSON文件</label>
                    <input type="file" id="file-upload" accept=".json,application/json">
                    <button id="deduplicate-btn" class="button" onclick="performGlobalDeduplication()">全局去重</button>
                    <button id="clear-data-btn" class="clear-btn">清空数据</button>
                </div>
            </div>
            <div class="data-display" id="data-display">
                <div class="placeholder" id="placeholder">
                    <div>
                        <strong>将JSON文件拖拽至此</strong><br>
                        或 <strong>粘贴JSON数据</strong> 到页面任意位置<br>
                        或点击 <strong>上传JSON文件</strong> 按钮
                    </div>
                </div>
                <table id="data-table" style="display: none;">
                    <thead id="table-head"></thead>
                    <tbody id="table-body"></tbody>
                </table>
            </div>
        </main>
    </div>
    
    <div class="drag-overlay" id="drag-overlay" style="display: none;">
        <div>释放JSON文件以解析</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const fileUpload = document.getElementById('file-upload');
            const clearDataBtn = document.getElementById('clear-data-btn');
            const table = document.getElementById('data-table');
            const tableHead = document.getElementById('table-head');
            const tableBody = document.getElementById('table-body');
            const placeholder = document.getElementById('placeholder');
            const dragOverlay = document.getElementById('drag-overlay');

            // Global data storage for all loaded data
            let globalDataArray = [];

            // Load saved field configurations on page load
            loadFieldConfigs();

            // Add event listeners for configuration inputs
            const listPath = document.getElementById('list-path');
            const primaryKeyPath = document.getElementById('primary-key-path');
            listPath.addEventListener('input', saveFieldConfigs);
            primaryKeyPath.addEventListener('input', saveFieldConfigs);

            // Add global deduplication event listener
            document.addEventListener('globalDeduplication', () => {
                if (globalDataArray.length === 0) {
                    alert('没有数据需要去重');
                    return;
                }

                const primaryKeyPath = document.getElementById('primary-key-path').value.trim();
                if (!primaryKeyPath) {
                    alert('请先配置主键路径才能进行去重');
                    return;
                }

                const originalCount = globalDataArray.length;
                globalDataArray = deduplicateData(globalDataArray, primaryKeyPath);
                const newCount = globalDataArray.length;

                // Update table with deduplicated data
                const configs = getFieldConfigs();
                if (configs.length > 0) {
                    updateTable(configs, globalDataArray);
                }

                alert(`去重完成！原有 ${originalCount} 条记录，去重后 ${newCount} 条记录，删除了 ${originalCount - newCount} 条重复记录。`);
            });

            // --- Event Listeners ---
            fileUpload.addEventListener('change', handleFileSelect);
            clearDataBtn.addEventListener('click', clearData);
            window.addEventListener('paste', handlePaste);
            
            // Drag and Drop Listeners
            window.addEventListener('dragenter', (e) => {
                e.preventDefault();
                dragOverlay.style.display = 'flex';
            });

            window.addEventListener('dragover', (e) => {
                e.preventDefault(); // Necessary to allow drop
            });

            window.addEventListener('dragleave', (e) => {
                // Hide overlay only if the mouse leaves the window entirely
                if (e.relatedTarget === null) {
                    dragOverlay.style.display = 'none';
                }
            });

            window.addEventListener('drop', (e) => {
                e.preventDefault();
                dragOverlay.style.display = 'none';
                if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0];
                    if (file.type === "application/json" || file.name.endsWith('.json')) {
                        readFile(file);
                    } else {
                        alert('请拖拽有效的JSON文件 (.json)');
                    }
                    e.dataTransfer.clearData();
                }
            });

            // --- Core Functions ---
            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) {
                    readFile(file);
                }
                // Reset file input to allow uploading the same file again
                event.target.value = '';
            }

            function handlePaste(event) {
                const targetTagName = event.target.tagName.toLowerCase();
                if (targetTagName === 'input' || targetTagName === 'textarea') {
                    return; // Don't process paste in input fields
                }
                const pastedData = (event.clipboardData || window.clipboardData).getData('text');
                processJsonData(pastedData);
            }

            function readFile(file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    processJsonData(e.target.result);
                };
                reader.onerror = () => {
                    alert('读取文件时出错');
                };
                reader.readAsText(file);
            }

            function processJsonData(jsonString) {
                let data;
                try {
                    data = JSON.parse(jsonString);
                } catch (error) {
                    alert(`JSON解析失败: ${error.message}`);
                    return;
                }

                const configs = getFieldConfigs();
                if (configs.length === 0 || configs.every(c => !c.path || !c.name)) {
                    alert('请至少配置一个有效的字段路径和字段名');
                    return;
                }

                // Extract data from list path if configured
                const listPath = document.getElementById('list-path').value.trim();
                let dataArray;

                if (listPath) {
                    try {
                        // Use JSONPath to extract list data
                        const extractedData = jsonpath.query(data, listPath);
                        if (extractedData && extractedData.length > 0) {
                            dataArray = extractedData;
                            console.log(`从列表路径 "${listPath}" 提取到 ${dataArray.length} 条记录`);
                        } else {
                            console.warn(`列表路径 "${listPath}" 未找到数据，使用原始数据`);
                            dataArray = Array.isArray(data) ? data : [data];
                        }
                    } catch (e) {
                        console.error(`列表路径 "${listPath}" 解析错误:`, e);
                        alert(`列表路径解析错误: ${e.message}`);
                        return;
                    }
                } else {
                    // Default behavior: ensure data is an array
                    dataArray = Array.isArray(data) ? data : [data];
                }

                // Add new data to global array
                globalDataArray = globalDataArray.concat(dataArray);

                // Apply global deduplication if primary key is configured
                const primaryKeyPath = document.getElementById('primary-key-path').value.trim();
                if (primaryKeyPath) {
                    globalDataArray = deduplicateData(globalDataArray, primaryKeyPath);
                    console.log(`全局去重后: ${globalDataArray.length} 条记录`);
                }

                // Update table with all data
                updateTable(configs, globalDataArray);
            }

            function getFieldConfigs() {
                const configRows = document.querySelectorAll('.field-config-row');
                const configs = [];
                configRows.forEach(row => {
                    const path = row.querySelector('.field-path').value.trim();
                    const name = row.querySelector('.field-name').value.trim();
                    if (path && name) {
                        configs.push({ path, name });
                    }
                });
                return configs;
            }

            function updateTable(configs, dataArray) {
                if (table.style.display === 'none') {
                    table.style.display = 'table';
                    placeholder.style.display = 'none';
                }

                // Update table header based on current configs
                tableHead.innerHTML = '';
                const headerRow = document.createElement('tr');
                configs.forEach(config => {
                    const th = document.createElement('th');
                    th.textContent = config.name;
                    headerRow.appendChild(th);
                });
                tableHead.appendChild(headerRow);

                // Clear existing table body and rebuild with all data
                tableBody.innerHTML = '';

                // Add all data rows
                dataArray.forEach(item => {
                    const row = document.createElement('tr');
                    configs.forEach(config => {
                        const td = document.createElement('td');
                        try {
                            // Use JSONPath to extract data
                            const result = jsonpath.query(item, config.path);
                            // Handle various result shapes from JSONPath
                            let displayText = 'N/A';
                            if (result && result.length > 0) {
                                displayText = result.map(r => (typeof r === 'object' ? JSON.stringify(r) : r)).join(', ');
                            }
                            td.textContent = displayText;
                        } catch (e) {
                            td.textContent = '路径错误';
                            console.error(`JSONPath error for path "${config.path}":`, e);
                        }
                        row.appendChild(td);
                    });
                    tableBody.appendChild(row);
                });
            }

            function clearData() {
                globalDataArray = []; // Clear global data
                tableBody.innerHTML = '';
                tableHead.innerHTML = '';
                table.style.display = 'none';
                placeholder.style.display = 'flex';
            }

            // --- Data Deduplication Function ---
            function deduplicateData(dataArray, primaryKeyPath) {
                const seen = new Set();
                const uniqueData = [];

                dataArray.forEach(item => {
                    try {
                        const result = jsonpath.query(item, primaryKeyPath);
                        let keyValue = null;

                        if (result && result.length > 0) {
                            // Use the first result as the key value
                            keyValue = result[0];
                            // Convert objects to string for comparison
                            if (typeof keyValue === 'object') {
                                keyValue = JSON.stringify(keyValue);
                            }
                        }

                        // If no key value found, treat as unique (include all items without valid key)
                        if (keyValue === null || keyValue === undefined) {
                            uniqueData.push(item);
                        } else if (!seen.has(keyValue)) {
                            seen.add(keyValue);
                            uniqueData.push(item);
                        }
                        // If key value already seen, skip this item (duplicate)
                    } catch (e) {
                        console.error(`Error extracting primary key for path "${primaryKeyPath}":`, e);
                        // Include items with key extraction errors
                        uniqueData.push(item);
                    }
                });

                console.log(`去重前: ${dataArray.length} 条记录, 去重后: ${uniqueData.length} 条记录`);
                return uniqueData;
            }



            // --- LocalStorage Functions ---
            function saveFieldConfigs() {
                const configs = getFieldConfigs();
                // Also include empty rows for UI state
                const allRows = document.querySelectorAll('.field-config-row');
                const allConfigs = [];
                allRows.forEach(row => {
                    const path = row.querySelector('.field-path').value.trim();
                    const name = row.querySelector('.field-name').value.trim();
                    allConfigs.push({ path, name });
                });

                // Save list path and primary key configuration
                const listPath = document.getElementById('list-path').value.trim();
                const primaryKeyPath = document.getElementById('primary-key-path').value.trim();

                const configData = {
                    fieldConfigs: allConfigs,
                    listPath: listPath,
                    primaryKey: {
                        path: primaryKeyPath
                    }
                };

                localStorage.setItem('jsonParserFieldConfigs', JSON.stringify(configData));
            }

            function loadFieldConfigs() {
                const savedConfigs = localStorage.getItem('jsonParserFieldConfigs');
                if (savedConfigs) {
                    try {
                        const configData = JSON.parse(savedConfigs);

                        // Handle both old format (array) and new format (object)
                        let fieldConfigs = [];
                        let listPath = '';
                        let primaryKey = { path: '' };

                        if (Array.isArray(configData)) {
                            // Old format - just field configs
                            fieldConfigs = configData;
                        } else if (configData && typeof configData === 'object') {
                            // New format - includes list path and primary key
                            fieldConfigs = configData.fieldConfigs || [];
                            listPath = configData.listPath || '';
                            primaryKey = configData.primaryKey || { path: '' };
                        }

                        // Load list path and primary key configuration
                        document.getElementById('list-path').value = listPath;
                        document.getElementById('primary-key-path').value = primaryKey.path || '';

                        if (fieldConfigs && fieldConfigs.length > 0) {
                            const container = document.getElementById('field-config-container');
                            // Clear existing rows
                            container.innerHTML = '';

                            // Add saved configurations
                            fieldConfigs.forEach((config, index) => {
                                const newRow = document.createElement('div');
                                newRow.className = 'field-config-row';
                                newRow.innerHTML = `
                                    <input type="text" class="field-name" placeholder="字段名称" value="${config.name || ''}">
                                    <input type="text" class="field-path" placeholder="字段路径" value="${config.path || ''}">
                                    <button class="icon-button ${index === 0 ? 'add-field-btn' : 'remove-field-btn'}" onclick="${index === 0 ? 'addField()' : 'removeField(this)'}">
                                        <svg viewBox="0 0 24 24"><path d="${index === 0 ? 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z' : 'M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'}"></path></svg>
                                    </button>
                                `;
                                container.appendChild(newRow);

                                // Add event listeners for auto-save
                                const nameInput = newRow.querySelector('.field-name');
                                const pathInput = newRow.querySelector('.field-path');
                                nameInput.addEventListener('input', saveFieldConfigs);
                                pathInput.addEventListener('input', saveFieldConfigs);
                            });

                            // Ensure at least one row exists
                            if (fieldConfigs.length === 0) {
                                addDefaultRow();
                            }
                        } else {
                            addDefaultRow();
                        }
                    } catch (e) {
                        console.error('Error loading saved configs:', e);
                        addDefaultRow();
                    }
                } else {
                    addDefaultRow();
                }
            }

            function addDefaultRow() {
                const container = document.getElementById('field-config-container');
                container.innerHTML = `
                    <div class="field-config-row">
                        <input type="text" class="field-name" placeholder="字段名称">
                        <input type="text" class="field-path" placeholder="字段路径">
                        <button class="icon-button add-field-btn" onclick="addField()">
                            <svg viewBox="0 0 24 24"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path></svg>
                        </button>
                    </div>
                `;
                // Add event listeners for auto-save
                const nameInput = container.querySelector('.field-name');
                const pathInput = container.querySelector('.field-path');
                nameInput.addEventListener('input', saveFieldConfigs);
                pathInput.addEventListener('input', saveFieldConfigs);
            }
        });

        // --- Global Functions for UI interaction ---
        function addField() {
            const container = document.getElementById('field-config-container');
            const newRow = document.createElement('div');
            newRow.className = 'field-config-row';
            newRow.innerHTML = `
                <input type="text" class="field-name" placeholder="字段名称">
                <input type="text" class="field-path" placeholder="字段路径">
                <button class="icon-button remove-field-btn" onclick="removeField(this)">
                    <svg viewBox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path></svg>
                </button>
            `;
            container.appendChild(newRow);

            // Add event listeners for auto-save
            const nameInput = newRow.querySelector('.field-name');
            const pathInput = newRow.querySelector('.field-path');
            nameInput.addEventListener('input', saveFieldConfigs);
            pathInput.addEventListener('input', saveFieldConfigs);

            // Save the new configuration
            saveFieldConfigs();
        }

        function removeField(button) {
            const container = document.getElementById('field-config-container');
            if (container.children.length > 1) {
                button.parentElement.remove();
                // Save the updated configuration
                saveFieldConfigs();
            } else {
                alert('至少需要保留一个字段配置');
            }
        }

        // Helper function to save configs (accessible globally)
        function saveFieldConfigs() {
            const configRows = document.querySelectorAll('.field-config-row');
            const allConfigs = [];
            configRows.forEach(row => {
                const path = row.querySelector('.field-path').value.trim();
                const name = row.querySelector('.field-name').value.trim();
                allConfigs.push({ path, name });
            });

            // Save list path and primary key configuration
            const listPath = document.getElementById('list-path').value.trim();
            const primaryKeyPath = document.getElementById('primary-key-path').value.trim();

            const configData = {
                fieldConfigs: allConfigs,
                listPath: listPath,
                primaryKey: {
                    path: primaryKeyPath
                }
            };

            localStorage.setItem('jsonParserFieldConfigs', JSON.stringify(configData));
        }

        // --- Global Deduplication Function ---
        function performGlobalDeduplication() {
            // Access the global data array from the DOMContentLoaded scope
            const tableBody = document.getElementById('table-body');
            const primaryKeyPath = document.getElementById('primary-key-path').value.trim();

            if (!primaryKeyPath) {
                alert('请先配置主键路径才能进行去重');
                return;
            }

            // Get current table data count
            const currentRowCount = tableBody.children.length;
            if (currentRowCount === 0) {
                alert('没有数据需要去重');
                return;
            }

            // Trigger a global deduplication by dispatching a custom event
            const event = new CustomEvent('globalDeduplication');
            document.dispatchEvent(event);
        }
    </script>

</body>
</html>
