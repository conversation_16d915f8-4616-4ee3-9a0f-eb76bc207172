<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址坐标聚类工具 - 基础版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
        }
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        .sidebar {
            width: 300px;
            background-color: #fff;
            padding: 1rem;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }
        .map-container {
            flex: 1;
            position: relative;
        }
        #map {
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .panel {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: #f9f9f9;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        .form-group input[type="file"],
        .form-group input[type="number"] {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin-bottom: 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header>
        <h1>地址坐标聚类工具 - 基础版</h1>
    </header>
    
    <div class="container">
        <div class="sidebar">
            <div class="panel">
                <h2>数据导入</h2>
                <div class="form-group">
                    <label for="fileInput">上传Excel文件：</label>
                    <input type="file" id="fileInput" accept=".xlsx,.xls">
                </div>
                <div class="form-group" id="addressCount" style="display: none;">
                    <p>已导入 <span id="count">0</span> 条地址数据</p>
                </div>
            </div>
            
            <div class="panel" id="clusterPanel" style="display: none;">
                <h2>聚类设置</h2>
                <div class="form-group">
                    <label for="clusterCount">分组数量：</label>
                    <input type="number" id="clusterCount" min="1" max="100" value="10">
                </div>
                <div class="form-group">
                    <button id="processBtn">开始处理</button>
                </div>
            </div>
            
            <div class="panel" id="resultPanel" style="display: none;">
                <h2>分组结果</h2>
                <div id="clusterStats"></div>
                <div class="form-group">
                    <button id="exportBtn">导出Excel</button>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                <div class="spinner"></div>
                <p id="processingMessage">处理中...</p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后执行
        window.onload = function() {
            console.log('页面加载完成');
            
            // 初始化地图
            initMap();
            
            // 绑定事件
            document.getElementById('fileInput').addEventListener('change', handleFileUpload);
            document.getElementById('processBtn').addEventListener('click', processAddresses);
            document.getElementById('exportBtn').addEventListener('click', exportExcel);
        };
        
        // 全局变量
        let map = null;
        let addresses = [];
        
        // 初始化地图
        function initMap() {
            // 这里可以添加地图初始化代码
            console.log('地图初始化');
            
            // 显示一个简单的地图占位符
            const mapElement = document.getElementById('map');
            mapElement.innerHTML = '<div style="padding: 20px; text-align: center;"><h3>地图区域</h3><p>这里将显示地图内容</p></div>';
        }
        
        // 处理文件上传
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            console.log('文件已选择:', file.name);
            
            // 模拟文件处理
            setTimeout(() => {
                // 模拟数据
                addresses = [
                    { 门店号: '001', 门店名称: '测试门店1', 省份: '北京', 城市: '北京', 区: '朝阳区', 详细地址: '三里屯' },
                    { 门店号: '002', 门店名称: '测试门店2', 省份: '北京', 城市: '北京', 区: '海淀区', 详细地址: '中关村' },
                    { 门店号: '003', 门店名称: '测试门店3', 省份: '上海', 城市: '上海', 区: '浦东新区', 详细地址: '陆家嘴' }
                ];
                
                // 更新UI
                document.getElementById('count').textContent = addresses.length;
                document.getElementById('addressCount').style.display = 'block';
                document.getElementById('clusterPanel').style.display = 'block';
                
                console.log('文件处理完成，导入了', addresses.length, '条数据');
            }, 500);
        }
        
        // 处理地址数据
        function processAddresses() {
            if (addresses.length === 0) {
                alert('请先上传地址数据');
                return;
            }
            
            // 显示加载中
            document.getElementById('loadingOverlay').style.display = 'flex';
            document.getElementById('processingMessage').textContent = '正在处理地址数据...';
            
            // 模拟处理过程
            setTimeout(() => {
                // 模拟聚类结果
                const clusters = [
                    { id: 1, count: 2 },
                    { id: 2, count: 1 }
                ];
                
                // 更新UI
                let statsHtml = '<p>共分为 ' + clusters.length + ' 组</p><ul>';
                clusters.forEach(cluster => {
                    statsHtml += '<li>组 ' + cluster.id + ': ' + cluster.count + ' 个点</li>';
                });
                statsHtml += '</ul>';
                
                document.getElementById('clusterStats').innerHTML = statsHtml;
                document.getElementById('resultPanel').style.display = 'block';
                
                // 隐藏加载中
                document.getElementById('loadingOverlay').style.display = 'none';
                
                console.log('处理完成，分为', clusters.length, '组');
            }, 2000);
        }
        
        // 导出Excel
        function exportExcel() {
            console.log('导出Excel');
            alert('导出功能在此示例中未实现');
        }
    </script>
</body>
</html>
