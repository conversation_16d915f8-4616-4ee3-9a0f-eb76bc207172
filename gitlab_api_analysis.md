# GitLab 社区版 15.7.6 数据查询API分析

## 版本兼容性说明
- **GitLab版本**: 社区版 15.7.6
- **API版本**: v4 (推荐)
- **认证方式**: Private Token / OAuth2
- **基础URL**: `https://********************.com/api/v4`

## 核心API端点

### 1. 项目相关API
```
GET /projects
GET /projects/:id
GET /projects/:id/repository/commits
GET /projects/:id/merge_requests
GET /projects/:id/members
```

### 2. 提交(Commit)数据API
```
# 获取项目提交列表
GET /projects/:id/repository/commits
参数:
- ref_name: 分支名 (默认: main/master)
- since: 开始时间 (ISO8601格式)
- until: 结束时间 (ISO8601格式)
- path: 文件路径过滤
- author: 作者邮箱/用户名
- per_page: 每页数量 (最大: 100)

# 获取单个提交详情
GET /projects/:id/repository/commits/:sha

# 获取提交的diff数据
GET /projects/:id/repository/commits/:sha/diff

# 获取提交统计信息
GET /projects/:id/repository/commits/:sha/stats
```

### 3. 合并请求(MR)数据API
```
# 获取项目MR列表
GET /projects/:id/merge_requests
参数:
- state: 状态 (opened/closed/merged/all)
- created_after: 创建时间过滤
- created_before: 创建时间过滤
- author_id: 作者ID
- assignee_id: 指派人ID
- per_page: 每页数量

# 获取MR详情
GET /projects/:id/merge_requests/:mr_id

# 获取MR的commits
GET /projects/:id/merge_requests/:mr_id/commits

# 获取MR的changes
GET /projects/:id/merge_requests/:mr_id/changes

# 获取MR的participants
GET /projects/:id/merge_requests/:mr_id/participants
```

### 4. 用户相关API
```
# 获取用户信息
GET /user
GET /users/:id

# 获取用户活动
GET /users/:id/events
参数:
- before: 时间过滤
- after: 时间过滤
- action: 活动类型
```

### 5. 统计相关API
```
# 项目贡献者统计
GET /projects/:id/repository/contributors

# 项目活动统计
GET /projects/:id/statistics

# 获取文件blame信息
GET /projects/:id/repository/files/:file_path/blame
```

## 代码量分析维度

### 1. 基础统计维度
| 维度 | API路径 | 返回数据 |
|------|---------|----------|
| 提交次数 | `/commits` | 提交总数 |
| 新增行数 | `/commits/:sha/stats` | additions |
| 删除行数 | `/commits/:sha/stats` | deletions |
| 总修改行数 | `/commits/:sha/stats` | total |
| 文件数 | `/commits/:sha/stats` | files_changed |

### 2. 时间维度分析
- **日维度**: 使用`since`和`until`参数按天过滤
- **周维度**: 计算每周的聚合数据
- **月维度**: 按月聚合统计数据
- **自定义范围**: 灵活设置时间窗口

### 3. 用户维度分析
- **个人统计**: 通过`author`参数过滤特定用户
- **团队统计**: 通过用户组ID过滤
- **对比分析**: 多用户数据对比

### 4. 项目维度分析
- **单项目**: 直接查询项目ID
- **多项目**: 遍历项目列表
- **分组统计**: 按项目分组统计

## 实际应用示例

### 1. 获取用户某月提交统计
```bash
# 获取某用户在2024年7月的所有提交
GET /projects/:id/repository/commits?author=<EMAIL>&since=2024-07-01T00:00:00Z&until=2024-07-31T23:59:59Z&per_page=100
```

### 2. 获取MR合并统计
```bash
# 获取某用户创建的已合并MR
GET /projects/:id/merge_requests?author_id=123&state=merged&created_after=2024-07-01&created_before=2024-07-31
```

### 3. 获取代码行数变化
```bash
# 获取某次提交的代码变化
GET /projects/:id/repository/commits/:sha/stats
返回示例:
{
  "additions": 150,
  "deletions": 75,
  "total": 225
}
```

## 限制和注意事项

### 1. 分页限制
- 默认每页20条
- 最大每页100条
- 需要使用`page`参数遍历所有数据

### 2. 速率限制
- 默认每分钟600次请求
- 需要处理429状态码重试
- 建议添加请求间隔

### 3. 数据完整性
- 大量数据需要多次API调用
- 建议添加数据缓存机制
- 考虑增量更新策略

### 4. 时间处理
- 所有时间使用UTC格式
- 本地时间需要转换
- 注意时区差异

## 推荐实现方案

### 1. 数据收集策略
```
1. 按项目遍历所有用户
2. 按时间窗口分批获取数据
3. 缓存中间结果避免重复查询
4. 增量更新减少API调用
```

### 2. 性能优化建议
- 使用GitLab GraphQL API (如有)减少请求次数
- 并行处理多个项目和用户
- 实现本地数据缓存
- 添加请求重试机制

### 3. 错误处理
- 处理网络异常和API限制
- 记录失败请求便于重试
- 实现优雅降级
- 添加监控告警

## 扩展API功能

### 1. Webhook集成
- 监听Push事件实时更新
- 监听Merge Request事件
- 监听Issue事件

### 2. 高级查询
- 使用GraphQL API (如果GitLab版本支持)
- 批量查询优化
- 自定义聚合查询

### 3. 数据导出
- 支持CSV/Excel导出
- 生成可视化报告
- 定时报告推送

## 实际部署考虑

### 1. 认证配置
```bash
# 获取Private Token
GitLab -> Settings -> Access Tokens -> 创建个人访问令牌

# 设置请求头
Headers: {
  'PRIVATE-TOKEN': 'your-private-token'
}
```

### 2. 环境配置
```bash
# 环境变量
GITLAB_URL=https://********************.com
GITLAB_TOKEN=your-private-token
PROJECT_IDS=1,2,3,4
```

### 3. 监控指标
- API调用次数和成功率
- 数据处理延迟
- 存储空间使用
- 错误率和响应时间

## 下一步建议

1. **需求细化**: 明确具体需要哪些统计维度
2. **原型验证**: 先用简单脚本验证API可用性
3. **架构设计**: 设计数据存储和处理架构
4. **权限申请**: 确认GitLab访问权限和令牌
5. **测试环境**: 准备测试项目和数据