<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
        }
        .container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        .sidebar {
            width: 300px;
            background-color: #fff;
            padding: 1rem;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }
        .map-container {
            flex: 1;
            position: relative;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .panel {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: #f9f9f9;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <header>
        <h1>最小化测试页面</h1>
    </header>
    
    <div class="container">
        <div class="sidebar">
            <div class="panel">
                <h2>控制面板</h2>
                <button onclick="showMessage()">点击我</button>
            </div>
        </div>
        
        <div class="map-container" id="map-container">
            <div style="padding: 20px;">
                <h2>地图区域</h2>
                <p>这里将显示地图内容</p>
                <p id="message"></p>
            </div>
        </div>
    </div>

    <script>
        function showMessage() {
            document.getElementById('message').textContent = '按钮已点击！' + new Date().toLocaleTimeString();
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            console.log('页面加载完成');
            document.getElementById('message').textContent = '页面已加载完成';
        };
    </script>
</body>
</html>
